"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useSession, signOut } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function DashboardPage() {
  const { data: session, isPending } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (!isPending && !session) {
      router.push("/auth/login");
    }
  }, [session, isPending, router]);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push("/");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">ngumpul.id</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                Halo, {session.user.name}
              </span>
              <Link href="/profile">
                <Button variant="outline" size="sm">
                  Profile
                </Button>
              </Link>
              <Button variant="outline" size="sm" onClick={handleSignOut}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Komunitas Saya</CardTitle>
                <CardDescription>
                  Kelola komunitas motor yang Anda ikuti
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  Lihat Komunitas
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Event Touring</CardTitle>
                <CardDescription>
                  Temukan dan ikuti event touring terbaru
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  Lihat Event
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tempat Viral</CardTitle>
                <CardDescription>
                  Jelajahi tempat-tempat viral untuk touring
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  Jelajahi Tempat
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Selamat Datang di ngumpul.id</CardTitle>
                <CardDescription>
                  Platform komunitas motor Indonesia
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-600">
                    Bergabunglah dengan komunitas motor terbesar di Indonesia. 
                    Temukan teman baru, ikuti event touring, dan jelajahi tempat-tempat 
                    menarik bersama komunitas motor.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Fitur Utama:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Bergabung dengan komunitas motor</li>
                        <li>• Ikuti event touring dan meetup</li>
                        <li>• Diskusi dengan sesama biker</li>
                        <li>• Temukan tempat viral untuk touring</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Mulai Sekarang:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Lengkapi profile Anda</li>
                        <li>• Cari komunitas di sekitar Anda</li>
                        <li>• Ikuti event yang menarik</li>
                        <li>• Bagikan pengalaman touring</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
