"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession, signOut } from "@/lib/auth-client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { INTEREST_CATEGORIES, type InterestCategory, type ActivityItem } from "@/lib/types";
import { Bell, MessageCircle, Heart, MapPin, Calendar, Users, Paperclip, Image } from "lucide-react";

// Mock data - in real app, this would come from API
const mockActivities: ActivityItem[] = [
  {
    id: "1",
    type: "touring",
    title: "Touring ke Bromo",
    description: "Yuk touring bareng ke Gunung Bromo weekend ini!",
    imageUrl: "/api/placeholder/400/200",
    location: "Malang, Jawa Timur",
    date: "2024-01-15",
    participantCount: 25,
    isLiked: false,
    likeCount: 12,
    commentCount: 8,
    user: {
      id: "user1",
      name: "<PERSON><PERSON>",
      avatar: "/api/placeholder/40/40",
      initial: "AP"
    }
  },
  {
    id: "2",
    type: "cafe",
    title: "Ngopi Bareng di Cafe Racer",
    description: "Kopdar santai sambil ngobrol motor",
    imageUrl: "/api/placeholder/400/200",
    location: "Jakarta Selatan",
    date: "2024-01-12",
    participantCount: 15,
    isLiked: true,
    likeCount: 8,
    commentCount: 5,
    user: {
      id: "user2",
      name: "Sari Dewi",
      avatar: "/api/placeholder/40/40",
      initial: "SD"
    }
  },
  {
    id: "3",
    type: "billiard",
    title: "Tournament Billiard Bulanan",
    description: "Kompetisi billiard untuk member komunitas",
    location: "Bandung",
    date: "2024-01-20",
    participantCount: 32,
    isLiked: false,
    likeCount: 15,
    commentCount: 12,
    user: {
      id: "user3",
      name: "Budi Santoso",
      initial: "BS"
    }
  }
];

export default function DashboardPage() {
  const { data: session, isPending } = useSession();
  const router = useRouter();
  const [selectedInterest, setSelectedInterest] = useState<InterestCategory>('touring');

  useEffect(() => {
    if (!isPending && !session) {
      router.push("/auth/login");
    }
  }, [session, isPending, router]);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push("/");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const filteredActivities = mockActivities.filter(activity => activity.type === selectedInterest);

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Top Navigation */}
      <nav className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700/50 sticky top-0 z-50">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10 ring-2 ring-blue-500/20">
                <AvatarImage src={session.user.image || ""} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                  {getUserInitials(session.user.name || "User")}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm text-slate-400">Welcome Back</p>
                <p className="font-semibold text-white">{session.user.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                className="text-slate-400 hover:text-white hover:bg-slate-700/50"
              >
                <Bell className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-slate-400 hover:text-white hover:bg-slate-700/50"
              >
                Logout
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="px-4 py-6 space-y-6">
        {/* Post Activity Section */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={session.user.image || ""} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                  {getUserInitials(session.user.name || "User")}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 bg-slate-700/50 rounded-full px-4 py-2 text-slate-400">
                Post your Activity
              </div>
              <div className="flex space-x-2">
                <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                  <Image className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interest Categories */}
        <div className="space-y-3">
          <p className="text-slate-400 text-sm">What do you up to?</p>
          <div className="flex space-x-3 overflow-x-auto pb-2">
            {INTEREST_CATEGORIES.map((category) => (
              <Button
                key={category.id}
                variant={selectedInterest === category.id ? "default" : "outline"}
                className={`flex-shrink-0 rounded-full px-6 py-2 ${
                  selectedInterest === category.id
                    ? "bg-yellow-500 text-black hover:bg-yellow-400"
                    : "border-slate-600 text-slate-300 hover:bg-slate-700/50"
                }`}
                onClick={() => setSelectedInterest(category.id)}
              >
                <span className="mr-2">{category.icon}</span>
                {category.label}
              </Button>
            ))}
          </div>
        </div>
