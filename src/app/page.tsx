"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useSession } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function Home() {
  const { data: session, isPending } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (!isPending && session) {
      router.push("/dashboard");
    }
  }, [session, isPending, router]);

  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">ngumpul.id</h1>
          <p className="text-xl text-gray-600 mb-8">
            Platform Komunitas Motor Terbesar di Indonesia
          </p>
          <p className="text-lg text-gray-500 max-w-2xl mx-auto">
            Bergabunglah dengan ribuan biker Indonesia. Temukan komunitas, ikuti
            event touring, dan jelajahi tempat-tempat menarik bersama.
          </p>
        </div>

        <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">🏍️ Komunitas</CardTitle>
              <CardDescription className="text-center">
                Bergabung dengan komunitas motor di seluruh Indonesia
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Temukan komunitas di sekitar Anda</li>
                <li>• Diskusi dengan sesama biker</li>
                <li>• Berbagi pengalaman touring</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">🗺️ Event Touring</CardTitle>
              <CardDescription className="text-center">
                Ikuti event touring dan meetup seru
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Event touring ke destinasi menarik</li>
                <li>• Meetup komunitas lokal</li>
                <li>• Jadwal dan rute perjalanan</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">📍 Tempat Viral</CardTitle>
              <CardDescription className="text-center">
                Jelajahi tempat-tempat viral untuk touring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Rekomendasi tempat wisata</li>
                <li>• Review dari biker lain</li>
                <li>• Peta dan navigasi</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="text-center space-y-6">
          <div className="space-x-4">
            <Link href="/auth/register">
              <Button size="lg" className="px-8 py-3 text-lg">
                Daftar Sekarang
              </Button>
            </Link>
            <Link href="/auth/login">
              <Button variant="outline" size="lg" className="px-8 py-3 text-lg">
                Masuk
              </Button>
            </Link>
          </div>
          <p className="text-sm text-gray-500">
            Gratis untuk semua biker Indonesia
          </p>
        </div>
      </div>

      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">ngumpul.id</h3>
              <p className="text-gray-400">
                Platform komunitas motor terbesar di Indonesia
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Fitur</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Komunitas Motor</li>
                <li>Event Touring</li>
                <li>Tempat Viral</li>
                <li>Diskusi</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Bantuan</h4>
              <ul className="space-y-2 text-gray-400">
                <li>FAQ</li>
                <li>Kontak</li>
                <li>Panduan</li>
                <li>Support</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Privacy Policy</li>
                <li>Terms of Service</li>
                <li>Cookie Policy</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 ngumpul.id. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
