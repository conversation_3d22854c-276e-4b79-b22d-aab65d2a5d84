import { pgTable, text, timestamp, uuid, boolean, integer, decimal, jsonb, varchar, pgEnum } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const joinTypeEnum = pgEnum('join_type', ['open', 'invitation', 'request']);
export const statusEnum = pgEnum('status', ['active', 'inactive', 'suspended']);
export const visibilityEnum = pgEnum('visibility', ['public', 'private']);
export const eventTypeEnum = pgEnum('event_type', ['touring', 'meetup']);
export const memberRoleEnum = pgEnum('member_role', ['admin', 'member']);

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  username: varchar('username', { length: 100 }).unique(),
  profileUrl: text('profile_url'),
  bio: text('bio'),
  location: varchar('location', { length: 255 }),
  phoneNumber: varchar('phone_number', { length: 20 }),
  emailVerified: boolean('email_verified').default(false),
  isAdmin: boolean('is_admin').default(false),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Communities table
export const communities = pgTable('communities', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  bannerUrl: text('banner_url'),
  profileUrl: text('profile_url'),
  tags: jsonb('tags').$type<string[]>(),
  location: varchar('location', { length: 255 }),
  foundedDate: timestamp('founded_date'),
  joinType: joinTypeEnum('join_type').default('open'),
  status: statusEnum('status').default('active'),
  visibility: visibilityEnum('visibility').default('public'),
  category: varchar('category', { length: 100 }),
  socialLinks: jsonb('social_links').$type<Record<string, string>>(),
  founderId: uuid('founder_id').references(() => users.id),
  memberCount: integer('member_count').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Community Members table
export const communityMembers = pgTable('community_members', {
  id: uuid('id').primaryKey().defaultRandom(),
  communityId: uuid('community_id').references(() => communities.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  role: memberRoleEnum('role').default('member'),
  joinedAt: timestamp('joined_at').defaultNow().notNull(),
  status: statusEnum('status').default('active'),
});

// Events table
export const events = pgTable('events', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  bannerUrl: text('banner_url'),
  eventType: eventTypeEnum('event_type').notNull(),
  startDate: timestamp('start_date').notNull(),
  endDate: timestamp('end_date'),
  location: varchar('location', { length: 255 }),
  destinationCoordinates: jsonb('destination_coordinates').$type<{lat: number, lng: number}>(),
  whatToBring: text('what_to_bring'),
  isPublic: boolean('is_public').default(true),
  donationSingle: decimal('donation_single', { precision: 10, scale: 2 }),
  donationCouple: decimal('donation_couple', { precision: 10, scale: 2 }),
  donationInfo: text('donation_info'),
  status: statusEnum('status').default('active'),
  communityId: uuid('community_id').references(() => communities.id).notNull(),
  createdById: uuid('created_by_id').references(() => users.id).notNull(),
  participantCount: integer('participant_count').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Event Schedule table
export const eventSchedule = pgTable('event_schedule', {
  id: uuid('id').primaryKey().defaultRandom(),
  eventId: uuid('event_id').references(() => events.id).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  scheduledTime: timestamp('scheduled_time').notNull(),
  location: varchar('location', { length: 255 }),
  coordinates: jsonb('coordinates').$type<{lat: number, lng: number}>(),
  type: varchar('type', { length: 50 }).notNull(), // 'meeting_point', 'checkpoint', 'break_point', 'finish_point'
  order: integer('order').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Event Participants table
export const eventParticipants = pgTable('event_participants', {
  id: uuid('id').primaryKey().defaultRandom(),
  eventId: uuid('event_id').references(() => events.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  participantType: varchar('participant_type', { length: 20 }).default('single'), // 'single', 'couple'
  status: varchar('status', { length: 20 }).default('registered'), // 'registered', 'confirmed', 'cancelled'
  registeredAt: timestamp('registered_at').defaultNow().notNull(),
});

// Viral Places table
export const viralPlaces = pgTable('viral_places', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  location: varchar('location', { length: 255 }).notNull(),
  coordinates: jsonb('coordinates').$type<{lat: number, lng: number}>(),
  imageUrls: jsonb('image_urls').$type<string[]>(),
  tags: jsonb('tags').$type<string[]>(),
  averageRating: decimal('average_rating', { precision: 3, scale: 2 }).default('0'),
  reviewCount: integer('review_count').default(0),
  createdById: uuid('created_by_id').references(() => users.id).notNull(),
  status: statusEnum('status').default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Reviews table
export const reviews = pgTable('reviews', {
  id: uuid('id').primaryKey().defaultRandom(),
  placeId: uuid('place_id').references(() => viralPlaces.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  rating: integer('rating').notNull(), // 1-5
  comment: text('comment'),
  imageUrls: jsonb('image_urls').$type<string[]>(),
  status: statusEnum('status').default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Posts table (for community discussions)
export const posts = pgTable('posts', {
  id: uuid('id').primaryKey().defaultRandom(),
  communityId: uuid('community_id').references(() => communities.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  title: varchar('title', { length: 255 }),
  content: text('content').notNull(),
  imageUrls: jsonb('image_urls').$type<string[]>(),
  likeCount: integer('like_count').default(0),
  commentCount: integer('comment_count').default(0),
  shareCount: integer('share_count').default(0),
  status: statusEnum('status').default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Comments table
export const comments = pgTable('comments', {
  id: uuid('id').primaryKey().defaultRandom(),
  postId: uuid('post_id').references(() => posts.id),
  parentCommentId: uuid('parent_comment_id').references(() => comments.id),
  userId: uuid('user_id').references(() => users.id).notNull(),
  content: text('content').notNull(),
  likeCount: integer('like_count').default(0),
  status: statusEnum('status').default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Likes table
export const likes = pgTable('likes', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  postId: uuid('post_id').references(() => posts.id),
  commentId: uuid('comment_id').references(() => comments.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  communities: many(communities),
  communityMemberships: many(communityMembers),
  events: many(events),
  eventParticipations: many(eventParticipants),
  posts: many(posts),
  comments: many(comments),
  likes: many(likes),
  reviews: many(reviews),
  viralPlaces: many(viralPlaces),
}));

export const communitiesRelations = relations(communities, ({ one, many }) => ({
  founder: one(users, {
    fields: [communities.founderId],
    references: [users.id],
  }),
  members: many(communityMembers),
  events: many(events),
  posts: many(posts),
}));

export const communityMembersRelations = relations(communityMembers, ({ one }) => ({
  community: one(communities, {
    fields: [communityMembers.communityId],
    references: [communities.id],
  }),
  user: one(users, {
    fields: [communityMembers.userId],
    references: [users.id],
  }),
}));

export const eventsRelations = relations(events, ({ one, many }) => ({
  community: one(communities, {
    fields: [events.communityId],
    references: [communities.id],
  }),
  createdBy: one(users, {
    fields: [events.createdById],
    references: [users.id],
  }),
  schedule: many(eventSchedule),
  participants: many(eventParticipants),
}));

export const eventScheduleRelations = relations(eventSchedule, ({ one }) => ({
  event: one(events, {
    fields: [eventSchedule.eventId],
    references: [events.id],
  }),
}));

export const eventParticipantsRelations = relations(eventParticipants, ({ one }) => ({
  event: one(events, {
    fields: [eventParticipants.eventId],
    references: [events.id],
  }),
  user: one(users, {
    fields: [eventParticipants.userId],
    references: [users.id],
  }),
}));

export const viralPlacesRelations = relations(viralPlaces, ({ one, many }) => ({
  createdBy: one(users, {
    fields: [viralPlaces.createdById],
    references: [users.id],
  }),
  reviews: many(reviews),
}));

export const reviewsRelations = relations(reviews, ({ one }) => ({
  place: one(viralPlaces, {
    fields: [reviews.placeId],
    references: [viralPlaces.id],
  }),
  user: one(users, {
    fields: [reviews.userId],
    references: [users.id],
  }),
}));

export const postsRelations = relations(posts, ({ one, many }) => ({
  community: one(communities, {
    fields: [posts.communityId],
    references: [communities.id],
  }),
  user: one(users, {
    fields: [posts.userId],
    references: [users.id],
  }),
  comments: many(comments),
  likes: many(likes),
}));

export const commentsRelations = relations(comments, ({ one, many }) => ({
  post: one(posts, {
    fields: [comments.postId],
    references: [posts.id],
  }),
  parentComment: one(comments, {
    fields: [comments.parentCommentId],
    references: [comments.id],
  }),
  user: one(users, {
    fields: [comments.userId],
    references: [users.id],
  }),
  replies: many(comments),
  likes: many(likes),
}));

export const likesRelations = relations(likes, ({ one }) => ({
  user: one(users, {
    fields: [likes.userId],
    references: [users.id],
  }),
  post: one(posts, {
    fields: [likes.postId],
    references: [posts.id],
  }),
  comment: one(comments, {
    fields: [likes.commentId],
    references: [comments.id],
  }),
}));
