import { InferSelectModel } from "drizzle-orm";
import * as schema from "../../drizzle/schema";

// User types
export type User = InferSelectModel<typeof schema.user>;
export type Profile = InferSelectModel<typeof schema.profile>;
export type UsersProfile = InferSelectModel<typeof schema.usersProfile>;

// Community types
export type Komunitas = InferSelectModel<typeof schema.komunitas>;
export type KomunitasMember = InferSelectModel<typeof schema.komunitasMember>;
export type KomunitasDiskusi = InferSelectModel<typeof schema.komunitasDiskusi>;
export type KomunitasDiskusiKomentar = InferSelectModel<typeof schema.komunitasDiskusiKomentar>;

// Event types
export type Events = InferSelectModel<typeof schema.events>;
export type EventsParticipant = InferSelectModel<typeof schema.eventsParticipant>;
export type EventsRoute = InferSelectModel<typeof schema.eventsRoute>;
export type EventComment = InferSelectModel<typeof schema.eventComment>;

// Places types
export type Places = InferSelectModel<typeof schema.places>;

// Activity types
export type UserActivity = InferSelectModel<typeof schema.userActivity>;

// Game types
export type BilliardGameSession = InferSelectModel<typeof schema.billiardGameSession>;
export type ChessGameSession = InferSelectModel<typeof schema.chessGameSession>;

// Extended types with relations
export interface KomunitasWithMembers extends Komunitas {
  members?: KomunitasMember[];
  memberCount?: number;
}

export interface EventsWithDetails extends Events {
  participants?: EventsParticipant[];
  participantCount?: number;
  route?: EventsRoute;
  comments?: EventComment[];
}

export interface UserWithProfile extends User {
  profile?: Profile;
  usersProfile?: UsersProfile;
}

// Activity types for the home feed
export type ActivityType = 'touring' | 'cafe' | 'billiard' | 'chess' | 'discussion' | 'event';

export interface ActivityItem {
  id: string;
  type: ActivityType;
  title: string;
  description?: string;
  imageUrl?: string;
  location?: string;
  date?: string;
  participantCount?: number;
  isLiked?: boolean;
  likeCount?: number;
  commentCount?: number;
  user?: {
    id: string;
    name: string;
    avatar?: string;
    initial?: string;
  };
}

// Home page data structure
export interface HomePageData {
  user: UserWithProfile;
  activities: ActivityItem[];
  nearbyKomunitas: KomunitasWithMembers[];
  upcomingEvents: EventsWithDetails[];
  trendingDiscussions: KomunitasDiskusi[];
  viralPlaces: Places[];
}

// Navigation types
export type NavigationItem = {
  id: string;
  label: string;
  icon: string;
  href: string;
  isActive?: boolean;
};

// Interest categories
export const INTEREST_CATEGORIES = [
  { id: 'touring', label: 'Touring', icon: '🏍️', color: 'bg-blue-500' },
  { id: 'cafe', label: 'Cafe', icon: '☕', color: 'bg-amber-500' },
  { id: 'billiard', label: 'Billiard', icon: '🎱', color: 'bg-green-500' },
  { id: 'chess', label: 'Chess', icon: '♟️', color: 'bg-purple-500' },
] as const;

export type InterestCategory = typeof INTEREST_CATEGORIES[number]['id'];
