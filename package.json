{"name": "ngumpul-v3", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.535.0", "next": "15.4.5", "postgres": "^3.4.7", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}