{"id": "********-0000-0000-0000-********0000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"account_providerId_accountId_key": {"name": "account_providerId_accountId_key", "columns": [{"expression": "providerId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "accountId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"account_userId_fkey": {"name": "account_userId_fkey", "tableFrom": "account", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.profile": {"name": "profile", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "dateOfBirth": {"name": "dateOfBirth", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "jobTitle": {"name": "jobTitle", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "default": "'en'"}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": false}, "initial": {"name": "initial", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "photo": {"name": "photo", "type": "text", "primaryKey": false, "notNull": false}, "profileCompleteness": {"name": "profileCompleteness", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {"profile_userId_key": {"name": "profile_userId_key", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "profile_username_key": {"name": "profile_username_key", "columns": [{"expression": "username", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"profile_userId_fkey": {"name": "profile_userId_fkey", "tableFrom": "profile", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.privacy_setting": {"name": "privacy_setting", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "profileVisibility": {"name": "profileVisibility", "type": "ProfileVisibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PRIVATE'"}, "emailVisibility": {"name": "emailVisibility", "type": "FieldVisibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PRIVATE'"}, "phoneVisibility": {"name": "phoneVisibility", "type": "FieldVisibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PRIVATE'"}, "locationVisibility": {"name": "locationVisibility", "type": "FieldVisibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PRIVATE'"}, "lastSeenVisibility": {"name": "lastSeenVisibility", "type": "FieldVisibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PRIVATE'"}}, "indexes": {"privacy_setting_userId_key": {"name": "privacy_setting_userId_key", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"privacy_setting_userId_fkey": {"name": "privacy_setting_userId_fkey", "tableFrom": "privacy_setting", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"verification_identifier_value_key": {"name": "verification_identifier_value_key", "columns": [{"expression": "identifier", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "value", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.notification_preference": {"name": "notification_preference", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "emailNotifications": {"name": "emailNotifications", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "securityAlerts": {"name": "securityAlerts", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "accountUpdates": {"name": "accountUpdates", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "productUpdates": {"name": "productUpdates", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "marketingEmails": {"name": "marketingEmails", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "smsNotifications": {"name": "smsNotifications", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "notificationFrequency": {"name": "notificationFrequency", "type": "NotificationFrequency", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'REALTIME'"}}, "indexes": {"notification_preference_userId_key": {"name": "notification_preference_userId_key", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notification_preference_userId_fkey": {"name": "notification_preference_userId_fkey", "tableFrom": "notification_preference", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.session_info": {"name": "session_info", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "sessionId": {"name": "sessionId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "deviceType": {"name": "deviceType", "type": "text", "primaryKey": false, "notNull": false}, "browser": {"name": "browser", "type": "text", "primaryKey": false, "notNull": false}, "operatingSystem": {"name": "operatingSystem", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "nickname": {"name": "nickname", "type": "text", "primaryKey": false, "notNull": false}, "isTrusted": {"name": "isTrusted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"session_info_sessionId_key": {"name": "session_info_sessionId_key", "columns": [{"expression": "sessionId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"session_info_sessionId_fkey": {"name": "session_info_sessionId_fkey", "tableFrom": "session_info", "tableTo": "session", "schemaTo": "public", "columnsFrom": ["sessionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.security_key": {"name": "security_key", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "keyId": {"name": "keyId", "type": "text", "primaryKey": false, "notNull": true}, "publicKey": {"name": "public<PERSON>ey", "type": "text", "primaryKey": false, "notNull": true}, "counter": {"name": "counter", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "lastUsed": {"name": "lastUsed", "type": "timestamp(3)", "primaryKey": false, "notNull": false}}, "indexes": {"security_key_keyId_key": {"name": "security_key_keyId_key", "columns": [{"expression": "keyId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"security_key_userId_fkey": {"name": "security_key_userId_fkey", "tableFrom": "security_key", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.login_history": {"name": "login_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "deviceType": {"name": "deviceType", "type": "text", "primaryKey": false, "notNull": false}, "browser": {"name": "browser", "type": "text", "primaryKey": false, "notNull": false}, "operatingSystem": {"name": "operatingSystem", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "failureReason": {"name": "failureReason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"login_history_userId_fkey": {"name": "login_history_userId_fkey", "tableFrom": "login_history", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "twoFactorEnabled": {"name": "twoFactorEnabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"user_email_key": {"name": "user_email_key", "columns": [{"expression": "email", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.events_participant": {"name": "events_participant", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "event_id": {"name": "event_id", "type": "bigint", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "total_pax": {"name": "total_pax", "type": "smallint", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "ticket_qr": {"name": "ticket_qr", "type": "text", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": false}, "admin_status": {"name": "admin_status", "type": "boolean", "primaryKey": false, "notNull": false}, "owner_status": {"name": "owner_status", "type": "boolean", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"events_participant_event_id_fkey": {"name": "events_participant_event_id_fkey", "tableFrom": "events_participant", "tableTo": "events", "schemaTo": "public", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "events_participant_user_id_fkey": {"name": "events_participant_user_id_fkey", "tableFrom": "events_participant", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "events_participant_usersProfileId_fkey": {"name": "events_participant_usersProfileId_fkey", "tableFrom": "events_participant", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_activity": {"name": "user_activity", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_activity_userId_fkey": {"name": "user_activity_userId_fkey", "tableFrom": "user_activity", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "public": {"name": "public", "type": "boolean", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "name2": {"name": "name2", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "cordinate": {"name": "cordinate", "type": "text", "primaryKey": false, "notNull": false}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "registration_close_date": {"name": "registration_close_date", "type": "date", "primaryKey": false, "notNull": false}, "donasi_single": {"name": "donasi_single", "type": "double precision", "primaryKey": false, "notNull": true, "default": 0}, "donasi_couple": {"name": "donasi_couple", "type": "double precision", "primaryKey": false, "notNull": true, "default": 0}, "donasi_custom": {"name": "donasi_custom", "type": "double precision", "primaryKey": false, "notNull": true, "default": 0}, "donasi_info": {"name": "donasi_info", "type": "text", "primaryKey": false, "notNull": false}, "banner": {"name": "banner", "type": "text", "primaryKey": false, "notNull": false}, "komunitas_id": {"name": "komunitas_id", "type": "bigint", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"events_komunitas_id_fkey": {"name": "events_komunitas_id_fkey", "tableFrom": "events", "tableTo": "komunitas", "schemaTo": "public", "columnsFrom": ["komunitas_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "events_created_by_fkey": {"name": "events_created_by_fkey", "tableFrom": "events", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "events_usersProfileId_fkey": {"name": "events_usersProfileId_fkey", "tableFrom": "events", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.events_route": {"name": "events_route", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "events_id": {"name": "events_id", "type": "bigint", "primaryKey": false, "notNull": false}, "grouping": {"name": "grouping", "type": "text", "primaryKey": false, "notNull": false}, "route_order": {"name": "route_order", "type": "bigint", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "point": {"name": "point", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "waktu": {"name": "waktu", "type": "timestamp(6)", "primaryKey": false, "notNull": false}, "cordinate": {"name": "cordinate", "type": "text", "primaryKey": false, "notNull": false}, "tanggal": {"name": "tanggal", "type": "date", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"events_route_events_id_fkey": {"name": "events_route_events_id_fkey", "tableFrom": "events_route", "tableTo": "events", "schemaTo": "public", "columnsFrom": ["events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.EventsRegQuestion": {"name": "EventsRegQuestion", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "event_id": {"name": "event_id", "type": "bigint", "primaryKey": false, "notNull": false}, "question": {"name": "question", "type": "text", "primaryKey": false, "notNull": false}, "answer_type": {"name": "answer_type", "type": "text", "primaryKey": false, "notNull": false}, "answer_choice": {"name": "answer_choice", "type": "text", "primaryKey": false, "notNull": false}, "urut": {"name": "urut", "type": "smallint", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"EventsRegQuestion_event_id_fkey": {"name": "EventsRegQuestion_event_id_fkey", "tableFrom": "EventsRegQuestion", "tableTo": "events", "schemaTo": "public", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.komunitas_member": {"name": "komunitas_member", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "komunitas_id": {"name": "komunitas_id", "type": "bigint", "primaryKey": false, "notNull": false}, "komunitas_member": {"name": "komunitas_member", "type": "text", "primaryKey": false, "notNull": false}, "member_role": {"name": "member_role", "type": "bigint", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"komunitas_member_komunitas_id_fkey": {"name": "komunitas_member_komunitas_id_fkey", "tableFrom": "komunitas_member", "tableTo": "komunitas", "schemaTo": "public", "columnsFrom": ["komunitas_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "komunitas_member_komunitas_member_fkey": {"name": "komunitas_member_komunitas_member_fkey", "tableFrom": "komunitas_member", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["komunitas_member"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_member_member_role_fkey": {"name": "komunitas_member_member_role_fkey", "tableFrom": "komunitas_member", "tableTo": "mfRoles_Komunitas", "schemaTo": "public", "columnsFrom": ["member_role"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_member_usersProfileId_fkey": {"name": "komunitas_member_usersProfileId_fkey", "tableFrom": "komunitas_member", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.EventsOTW": {"name": "EventsOTW", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "event_id": {"name": "event_id", "type": "bigint", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "waktu_otw": {"name": "waktu_otw", "type": "timestamp(6)", "primaryKey": false, "notNull": false}, "waktu_update": {"name": "waktu_update", "type": "timestamp(6)", "primaryKey": false, "notNull": false}, "lokasi_otw": {"name": "lokasi_otw", "type": "text", "primaryKey": false, "notNull": false}, "lokasi_update": {"name": "lokasi_update", "type": "text", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"EventsOTW_event_id_fkey": {"name": "EventsOTW_event_id_fkey", "tableFrom": "EventsOTW", "tableTo": "events", "schemaTo": "public", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "EventsOTW_user_id_fkey": {"name": "EventsOTW_user_id_fkey", "tableFrom": "EventsOTW", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "EventsOTW_usersProfileId_fkey": {"name": "EventsOTW_usersProfileId_fkey", "tableFrom": "EventsOTW", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.komunitas_diskusi": {"name": "komunitas_diskusi", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "komunitas_id": {"name": "komunitas_id", "type": "bigint", "primaryKey": false, "notNull": false}, "topik": {"name": "topik", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "like": {"name": "like", "type": "smallint", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"komunitas_diskusi_komunitas_id_fkey": {"name": "komunitas_diskusi_komunitas_id_fkey", "tableFrom": "komunitas_diskusi", "tableTo": "komunitas", "schemaTo": "public", "columnsFrom": ["komunitas_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_diskusi_user_id_fkey": {"name": "komunitas_diskusi_user_id_fkey", "tableFrom": "komunitas_diskusi", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_diskusi_usersProfileId_fkey": {"name": "komunitas_diskusi_usersProfileId_fkey", "tableFrom": "komunitas_diskusi", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.komunitas_diskusi_komentar": {"name": "komunitas_diskusi_komentar", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "komunitas_diskusi_id": {"name": "komunitas_diskusi_id", "type": "bigint", "primaryKey": false, "notNull": false}, "komentar": {"name": "komentar", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "like": {"name": "like", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"komunitas_diskusi_komentar_komunitas_diskusi_id_fkey": {"name": "komunitas_diskusi_komentar_komunitas_diskusi_id_fkey", "tableFrom": "komunitas_diskusi_komentar", "tableTo": "komunitas_diskusi", "schemaTo": "public", "columnsFrom": ["komunitas_diskusi_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_diskusi_komentar_user_id_fkey": {"name": "komunitas_diskusi_komentar_user_id_fkey", "tableFrom": "komunitas_diskusi_komentar", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_diskusi_komentar_usersProfileId_fkey": {"name": "komunitas_diskusi_komentar_usersProfileId_fkey", "tableFrom": "komunitas_diskusi_komentar", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.komunitas": {"name": "komunitas", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "nama": {"name": "nama", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "since": {"name": "since", "type": "timestamp(6)", "primaryKey": false, "notNull": false}, "founder": {"name": "founder", "type": "text", "primaryKey": false, "notNull": false}, "short_title": {"name": "short_title", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "nama2": {"name": "nama2", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "join": {"name": "join", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "profile_Image": {"name": "profile_Image", "type": "text", "primaryKey": false, "notNull": false}, "banner_image": {"name": "banner_image", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.places": {"name": "places", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "area": {"name": "area", "type": "text", "primaryKey": false, "notNull": false}, "deskripsi": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "latitude": {"name": "latitude", "type": "text", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "short_des": {"name": "short_des", "type": "text", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"places_user_id_fkey": {"name": "places_user_id_fkey", "tableFrom": "places", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "places_usersProfileId_fkey": {"name": "places_usersProfileId_fkey", "tableFrom": "places", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.mfRoles_Komunitas": {"name": "mfRoles_Komunitas", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "default": "'15'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.komunitas_like_by": {"name": "komunitas_like_by", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "komunitas_id": {"name": "komunitas_id", "type": "bigint", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "like": {"name": "like", "type": "bigint", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"komunitas_like_by_komunitas_id_fkey": {"name": "komunitas_like_by_komunitas_id_fkey", "tableFrom": "komunitas_like_by", "tableTo": "komunitas", "schemaTo": "public", "columnsFrom": ["komunitas_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_like_by_user_id_fkey": {"name": "komunitas_like_by_user_id_fkey", "tableFrom": "komunitas_like_by", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "komunitas_like_by_usersProfileId_fkey": {"name": "komunitas_like_by_usersProfileId_fkey", "tableFrom": "komunitas_like_by", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"session_token_key": {"name": "session_token_key", "columns": [{"expression": "token", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"session_userId_fkey": {"name": "session_userId_fkey", "tableFrom": "session", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users_profile": {"name": "users_profile", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": false}, "initial": {"name": "initial", "type": "text", "primaryKey": false, "notNull": false}, "tgl_lahir": {"name": "tgl_lahir", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "photo": {"name": "photo", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.events_like_by": {"name": "events_like_by", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "event_id": {"name": "event_id", "type": "bigint", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "like": {"name": "like", "type": "bigint", "primaryKey": false, "notNull": false}, "usersProfileId": {"name": "usersProfileId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"events_like_by_event_id_fkey": {"name": "events_like_by_event_id_fkey", "tableFrom": "events_like_by", "tableTo": "events", "schemaTo": "public", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "events_like_by_user_id_fkey": {"name": "events_like_by_user_id_fkey", "tableFrom": "events_like_by", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "events_like_by_usersProfileId_fkey": {"name": "events_like_by_usersProfileId_fkey", "tableFrom": "events_like_by", "tableTo": "users_profile", "schemaTo": "public", "columnsFrom": ["usersProfileId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.event_comment": {"name": "event_comment", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "eventId": {"name": "eventId", "type": "bigint", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "parentId": {"name": "parentId", "type": "bigint", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "likeCount": {"name": "likeCount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"event_comment_eventId_fkey": {"name": "event_comment_eventId_fkey", "tableFrom": "event_comment", "tableTo": "events", "schemaTo": "public", "columnsFrom": ["eventId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "event_comment_userId_fkey": {"name": "event_comment_userId_fkey", "tableFrom": "event_comment", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "event_comment_parentId_fkey": {"name": "event_comment_parentId_fkey", "tableFrom": "event_comment", "tableTo": "event_comment", "schemaTo": "public", "columnsFrom": ["parentId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.event_comment_like": {"name": "event_comment_like", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "commentId": {"name": "commentId", "type": "bigint", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"event_comment_like_commentId_userId_key": {"name": "event_comment_like_commentId_userId_key", "columns": [{"expression": "commentId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "userId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"event_comment_like_commentId_fkey": {"name": "event_comment_like_commentId_fkey", "tableFrom": "event_comment_like", "tableTo": "event_comment", "schemaTo": "public", "columnsFrom": ["commentId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "event_comment_like_userId_fkey": {"name": "event_comment_like_userId_fkey", "tableFrom": "event_comment_like", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chess_game_round": {"name": "chess_game_round", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "roundNumber": {"name": "roundNumber", "type": "integer", "primaryKey": false, "notNull": true}, "winnerId": {"name": "winnerId", "type": "text", "primaryKey": false, "notNull": false}, "winnerName": {"name": "winner<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "whitePlayerId": {"name": "whitePlayerId", "type": "text", "primaryKey": false, "notNull": true}, "blackPlayerId": {"name": "blackPlayerId", "type": "text", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "gameMode": {"name": "gameMode", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "timeControl": {"name": "timeControl", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "pgnMoves": {"name": "pgnMoves", "type": "text", "primaryKey": false, "notNull": false}, "roundNotes": {"name": "roundNotes", "type": "text", "primaryKey": false, "notNull": false}, "startedAt": {"name": "startedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "completedAt": {"name": "completedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}}, "indexes": {"chess_game_round_gameSessionId_roundNumber_key": {"name": "chess_game_round_gameSessionId_roundNumber_key", "columns": [{"expression": "gameSessionId", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}, {"expression": "roundNumber", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chess_game_round_gameSessionId_fkey": {"name": "chess_game_round_gameSessionId_fkey", "tableFrom": "chess_game_round", "tableTo": "chess_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_game_round_winnerId_fkey": {"name": "chess_game_round_winnerId_fkey", "tableFrom": "chess_game_round", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["winnerId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "chess_game_round_whitePlayerId_fkey": {"name": "chess_game_round_whitePlayerId_fkey", "tableFrom": "chess_game_round", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["whitePlayerId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_game_round_blackPlayerId_fkey": {"name": "chess_game_round_blackPlayerId_fkey", "tableFrom": "chess_game_round", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["blackPlayerId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.billiard_round_player": {"name": "billiard_round_player", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "roundId": {"name": "roundId", "type": "bigint", "primaryKey": false, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "playerId": {"name": "playerId", "type": "text", "primaryKey": false, "notNull": true}, "playerName": {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "participationStatus": {"name": "participationStatus", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "betAmount": {"name": "betAmount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"billiard_round_player_roundId_playerId_key": {"name": "billiard_round_player_roundId_playerId_key", "columns": [{"expression": "roundId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "playerId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"billiard_round_player_roundId_fkey": {"name": "billiard_round_player_roundId_fkey", "tableFrom": "billiard_round_player", "tableTo": "billiard_game_round", "schemaTo": "public", "columnsFrom": ["roundId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "billiard_round_player_gameSessionId_fkey": {"name": "billiard_round_player_gameSessionId_fkey", "tableFrom": "billiard_round_player", "tableTo": "billiard_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "billiard_round_player_playerId_fkey": {"name": "billiard_round_player_playerId_fkey", "tableFrom": "billiard_round_player", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["playerId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.event_location_share": {"name": "event_location_share", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "eventId": {"name": "eventId", "type": "bigint", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "latitude": {"name": "latitude", "type": "double precision", "primaryKey": false, "notNull": true}, "longitude": {"name": "longitude", "type": "double precision", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"event_location_share_eventId_userId_key": {"name": "event_location_share_eventId_userId_key", "columns": [{"expression": "eventId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "userId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"event_location_share_eventId_fkey": {"name": "event_location_share_eventId_fkey", "tableFrom": "event_location_share", "tableTo": "events", "schemaTo": "public", "columnsFrom": ["eventId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "event_location_share_userId_fkey": {"name": "event_location_share_userId_fkey", "tableFrom": "event_location_share", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.billiard_game_session": {"name": "billiard_game_session", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "komunitasId": {"name": "komunitasId", "type": "bigint", "primaryKey": false, "notNull": true}, "gameName": {"name": "gameName", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "gameRules": {"name": "gameRules", "type": "text", "primaryKey": false, "notNull": false}, "betAmount": {"name": "betAmount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "penaltyAmount": {"name": "penaltyAmount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "gameDate": {"name": "gameDate", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "totalRounds": {"name": "totalRounds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "createdBy": {"name": "created<PERSON>y", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "gameType": {"name": "gameType", "type": "GameType", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'BILLIARD'"}}, "indexes": {}, "foreignKeys": {"billiard_game_session_komunitasId_fkey": {"name": "billiard_game_session_komunitasId_fkey", "tableFrom": "billiard_game_session", "tableTo": "komunitas", "schemaTo": "public", "columnsFrom": ["komunitasId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "billiard_game_session_createdBy_fkey": {"name": "billiard_game_session_createdBy_fkey", "tableFrom": "billiard_game_session", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["created<PERSON>y"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.billiard_game_player": {"name": "billiard_game_player", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "playerId": {"name": "playerId", "type": "text", "primaryKey": false, "notNull": true}, "playerName": {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "joinedAt": {"name": "joinedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"billiard_game_player_gameSessionId_playerId_key": {"name": "billiard_game_player_gameSessionId_playerId_key", "columns": [{"expression": "gameSessionId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "playerId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"billiard_game_player_gameSessionId_fkey": {"name": "billiard_game_player_gameSessionId_fkey", "tableFrom": "billiard_game_player", "tableTo": "billiard_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "billiard_game_player_playerId_fkey": {"name": "billiard_game_player_playerId_fkey", "tableFrom": "billiard_game_player", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["playerId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.billiard_game_round": {"name": "billiard_game_round", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "roundNumber": {"name": "roundNumber", "type": "integer", "primaryKey": false, "notNull": true}, "winnerId": {"name": "winnerId", "type": "text", "primaryKey": false, "notNull": true}, "winnerName": {"name": "winner<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "roundNotes": {"name": "roundNotes", "type": "text", "primaryKey": false, "notNull": false}, "startedAt": {"name": "startedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "completedAt": {"name": "completedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}}, "indexes": {"billiard_game_round_gameSessionId_roundNumber_key": {"name": "billiard_game_round_gameSessionId_roundNumber_key", "columns": [{"expression": "gameSessionId", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}, {"expression": "roundNumber", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"billiard_game_round_gameSessionId_fkey": {"name": "billiard_game_round_gameSessionId_fkey", "tableFrom": "billiard_game_round", "tableTo": "billiard_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "billiard_game_round_winnerId_fkey": {"name": "billiard_game_round_winnerId_fkey", "tableFrom": "billiard_game_round", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["winnerId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.billiard_transaction": {"name": "billiard_transaction", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "roundId": {"name": "roundId", "type": "bigint", "primaryKey": false, "notNull": true}, "roundNumber": {"name": "roundNumber", "type": "integer", "primaryKey": false, "notNull": true}, "transactionType": {"name": "transactionType", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "fromPlayerId": {"name": "fromPlayerId", "type": "text", "primaryKey": false, "notNull": false}, "fromPlayerName": {"name": "fromPlayerName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "toPlayerId": {"name": "toPlayerId", "type": "text", "primaryKey": false, "notNull": false}, "toPlayerName": {"name": "toPlayerName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "faultReason": {"name": "faultReason", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"billiard_transaction_gameSessionId_fkey": {"name": "billiard_transaction_gameSessionId_fkey", "tableFrom": "billiard_transaction", "tableTo": "billiard_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "billiard_transaction_roundId_fkey": {"name": "billiard_transaction_roundId_fkey", "tableFrom": "billiard_transaction", "tableTo": "billiard_game_round", "schemaTo": "public", "columnsFrom": ["roundId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "billiard_transaction_fromPlayerId_fkey": {"name": "billiard_transaction_fromPlayerId_fkey", "tableFrom": "billiard_transaction", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["fromPlayerId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "billiard_transaction_toPlayerId_fkey": {"name": "billiard_transaction_toPlayerId_fkey", "tableFrom": "billiard_transaction", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["toPlayerId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chess_game_player": {"name": "chess_game_player", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "playerId": {"name": "playerId", "type": "text", "primaryKey": false, "notNull": true}, "playerName": {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "eloRating": {"name": "eloRating", "type": "integer", "primaryKey": false, "notNull": true, "default": 1200}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "joinedAt": {"name": "joinedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"chess_game_player_gameSessionId_playerId_key": {"name": "chess_game_player_gameSessionId_playerId_key", "columns": [{"expression": "gameSessionId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "playerId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chess_game_player_gameSessionId_fkey": {"name": "chess_game_player_gameSessionId_fkey", "tableFrom": "chess_game_player", "tableTo": "chess_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_game_player_playerId_fkey": {"name": "chess_game_player_playerId_fkey", "tableFrom": "chess_game_player", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["playerId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chess_game_session": {"name": "chess_game_session", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "komunitasId": {"name": "komunitasId", "type": "bigint", "primaryKey": false, "notNull": true}, "gameType": {"name": "gameType", "type": "GameType", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'CHESS'"}, "gameName": {"name": "gameName", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "gameRules": {"name": "gameRules", "type": "text", "primaryKey": false, "notNull": false}, "betAmount": {"name": "betAmount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "penaltyAmount": {"name": "penaltyAmount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "gameDate": {"name": "gameDate", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "totalRounds": {"name": "totalRounds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "createdBy": {"name": "created<PERSON>y", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"chess_game_session_createdBy_fkey": {"name": "chess_game_session_createdBy_fkey", "tableFrom": "chess_game_session", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["created<PERSON>y"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_game_session_komunitasId_fkey": {"name": "chess_game_session_komunitasId_fkey", "tableFrom": "chess_game_session", "tableTo": "komunitas", "schemaTo": "public", "columnsFrom": ["komunitasId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chess_round_player": {"name": "chess_round_player", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "roundId": {"name": "roundId", "type": "bigint", "primaryKey": false, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "playerId": {"name": "playerId", "type": "text", "primaryKey": false, "notNull": true}, "playerName": {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "participationStatus": {"name": "participationStatus", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "betAmount": {"name": "betAmount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "eloChange": {"name": "eloChange", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"chess_round_player_roundId_playerId_key": {"name": "chess_round_player_roundId_playerId_key", "columns": [{"expression": "roundId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "playerId", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chess_round_player_roundId_fkey": {"name": "chess_round_player_roundId_fkey", "tableFrom": "chess_round_player", "tableTo": "chess_game_round", "schemaTo": "public", "columnsFrom": ["roundId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_round_player_gameSessionId_fkey": {"name": "chess_round_player_gameSessionId_fkey", "tableFrom": "chess_round_player", "tableTo": "chess_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_round_player_playerId_fkey": {"name": "chess_round_player_playerId_fkey", "tableFrom": "chess_round_player", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["playerId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chess_transaction": {"name": "chess_transaction", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "gameSessionId": {"name": "gameSessionId", "type": "bigint", "primaryKey": false, "notNull": true}, "roundId": {"name": "roundId", "type": "bigint", "primaryKey": false, "notNull": true}, "roundNumber": {"name": "roundNumber", "type": "integer", "primaryKey": false, "notNull": true}, "transactionType": {"name": "transactionType", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "fromPlayerId": {"name": "fromPlayerId", "type": "text", "primaryKey": false, "notNull": false}, "fromPlayerName": {"name": "fromPlayerName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "toPlayerId": {"name": "toPlayerId", "type": "text", "primaryKey": false, "notNull": false}, "toPlayerName": {"name": "toPlayerName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "faultReason": {"name": "faultReason", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "eloChange": {"name": "eloChange", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"chess_transaction_fromPlayerId_fkey": {"name": "chess_transaction_fromPlayerId_fkey", "tableFrom": "chess_transaction", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["fromPlayerId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}, "chess_transaction_gameSessionId_fkey": {"name": "chess_transaction_gameSessionId_fkey", "tableFrom": "chess_transaction", "tableTo": "chess_game_session", "schemaTo": "public", "columnsFrom": ["gameSessionId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_transaction_roundId_fkey": {"name": "chess_transaction_roundId_fkey", "tableFrom": "chess_transaction", "tableTo": "chess_game_round", "schemaTo": "public", "columnsFrom": ["roundId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "chess_transaction_toPlayerId_fkey": {"name": "chess_transaction_toPlayerId_fkey", "tableFrom": "chess_transaction", "tableTo": "profile", "schemaTo": "public", "columnsFrom": ["toPlayerId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.FieldVisibility": {"name": "FieldVisibility", "values": ["PUBLIC", "PRIVATE", "HIDDEN"], "schema": "public"}, "public.GameType": {"name": "GameType", "values": ["BILLIARD", "CHESS"], "schema": "public"}, "public.NotificationFrequency": {"name": "NotificationFrequency", "values": ["REALTIME", "DAILY", "WEEKLY", "MONTHLY"], "schema": "public"}, "public.ProfileVisibility": {"name": "ProfileVisibility", "values": ["PUBLIC", "PRIVATE", "REGISTERED_USERS_ONLY"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}