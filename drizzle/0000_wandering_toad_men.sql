-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."FieldVisibility" AS ENUM('PUBLIC', 'PRIVATE', 'HIDDEN');--> statement-breakpoint
CREATE TYPE "public"."GameType" AS ENUM('B<PERSON><PERSON>IARD', 'CHESS');--> statement-breakpoint
CREATE TYPE "public"."NotificationFrequency" AS ENUM('REALTIME', 'DAILY', 'WEEKLY', 'MONTHLY');--> statement-breakpoint
CREATE TYPE "public"."ProfileVisibility" AS ENUM('PUBLIC', 'PRIVATE', 'REGISTERED_USERS_ONLY');--> statement-breakpoint
CREATE TABLE "account" (
	"id" text PRIMARY KEY NOT NULL,
	"accountId" text NOT NULL,
	"providerId" text NOT NULL,
	"userId" text NOT NULL,
	"accessToken" text,
	"refreshToken" text,
	"idToken" text,
	"accessTokenExpiresAt" timestamp(3),
	"refreshTokenExpiresAt" timestamp(3),
	"scope" text,
	"password" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "profile" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"firstName" text,
	"lastName" text,
	"username" text,
	"phone" text,
	"bio" text,
	"dateOfBirth" timestamp(3),
	"location" text,
	"jobTitle" text,
	"website" text,
	"timezone" text DEFAULT 'UTC',
	"language" text DEFAULT 'en',
	"full_name" text,
	"initial" text,
	"gender" text,
	"photo" text,
	"profileCompleteness" integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "privacy_setting" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"profileVisibility" "ProfileVisibility" DEFAULT 'PRIVATE' NOT NULL,
	"emailVisibility" "FieldVisibility" DEFAULT 'PRIVATE' NOT NULL,
	"phoneVisibility" "FieldVisibility" DEFAULT 'PRIVATE' NOT NULL,
	"locationVisibility" "FieldVisibility" DEFAULT 'PRIVATE' NOT NULL,
	"lastSeenVisibility" "FieldVisibility" DEFAULT 'PRIVATE' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "verification" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expiresAt" timestamp(3) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "notification_preference" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"emailNotifications" boolean DEFAULT true NOT NULL,
	"securityAlerts" boolean DEFAULT true NOT NULL,
	"accountUpdates" boolean DEFAULT true NOT NULL,
	"productUpdates" boolean DEFAULT false NOT NULL,
	"marketingEmails" boolean DEFAULT false NOT NULL,
	"smsNotifications" boolean DEFAULT false NOT NULL,
	"notificationFrequency" "NotificationFrequency" DEFAULT 'REALTIME' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "session_info" (
	"id" text PRIMARY KEY NOT NULL,
	"sessionId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"deviceType" text,
	"browser" text,
	"operatingSystem" text,
	"location" text,
	"country" text,
	"city" text,
	"nickname" text,
	"isTrusted" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "security_key" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"name" text NOT NULL,
	"keyId" text NOT NULL,
	"publicKey" text NOT NULL,
	"counter" integer DEFAULT 0 NOT NULL,
	"lastUsed" timestamp(3)
);
--> statement-breakpoint
CREATE TABLE "login_history" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"ipAddress" text,
	"userAgent" text,
	"deviceType" text,
	"browser" text,
	"operatingSystem" text,
	"location" text,
	"country" text,
	"city" text,
	"success" boolean DEFAULT true NOT NULL,
	"failureReason" text
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"emailVerified" boolean DEFAULT false NOT NULL,
	"image" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"twoFactorEnabled" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "events_participant" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"event_id" bigint,
	"user_id" text,
	"total_pax" smallint,
	"status" text,
	"ticket_qr" text,
	"payment_status" text,
	"admin_status" boolean,
	"owner_status" boolean,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "user_activity" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"action" text NOT NULL,
	"description" text,
	"ipAddress" text,
	"userAgent" text,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "events" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"public" boolean,
	"name" text,
	"name2" text,
	"description" text,
	"start_date" date,
	"end_date" date,
	"status" text,
	"location" text,
	"cordinate" text,
	"event_type" text,
	"created_by" text,
	"registration_close_date" date,
	"donasi_single" double precision DEFAULT 0 NOT NULL,
	"donasi_couple" double precision DEFAULT 0 NOT NULL,
	"donasi_custom" double precision DEFAULT 0 NOT NULL,
	"donasi_info" text,
	"banner" text,
	"komunitas_id" bigint,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "events_route" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"events_id" bigint,
	"grouping" text,
	"route_order" bigint,
	"name" text,
	"point" text,
	"description" text,
	"waktu" timestamp(6),
	"cordinate" text,
	"tanggal" date
);
--> statement-breakpoint
CREATE TABLE "EventsRegQuestion" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"event_id" bigint,
	"question" text,
	"answer_type" text,
	"answer_choice" text,
	"urut" smallint
);
--> statement-breakpoint
CREATE TABLE "komunitas_member" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"komunitas_id" bigint,
	"komunitas_member" text,
	"member_role" bigint,
	"status" text,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "EventsOTW" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"event_id" bigint,
	"user_id" text,
	"status" text,
	"waktu_otw" timestamp(6),
	"waktu_update" timestamp(6),
	"lokasi_otw" text,
	"lokasi_update" text,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "komunitas_diskusi" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"komunitas_id" bigint,
	"topik" text,
	"image" text,
	"like" smallint,
	"status" text,
	"user_id" text,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "komunitas_diskusi_komentar" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"komunitas_diskusi_id" bigint,
	"komentar" text,
	"image" text,
	"like" integer,
	"user_id" text,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "komunitas" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"nama" text,
	"description" text,
	"type" text DEFAULT '',
	"since" timestamp(6),
	"founder" text,
	"short_title" text DEFAULT '',
	"nama2" text,
	"status" text DEFAULT '',
	"join" text DEFAULT '',
	"profile_Image" text,
	"banner_image" text
);
--> statement-breakpoint
CREATE TABLE "places" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"user_id" text,
	"name" text,
	"area" text,
	"deskripsi" text,
	"latitude" text,
	"longitude" text,
	"image" text,
	"short_des" text,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "mfRoles_Komunitas" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"role" text DEFAULT '15'
);
--> statement-breakpoint
CREATE TABLE "komunitas_like_by" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"komunitas_id" bigint,
	"user_id" text,
	"like" bigint,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "session" (
	"id" text PRIMARY KEY NOT NULL,
	"expiresAt" timestamp(3) NOT NULL,
	"token" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"ipAddress" text,
	"userAgent" text,
	"userId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users_profile" (
	"id" uuid PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"full_name" text,
	"initial" text,
	"tgl_lahir" date,
	"gender" text,
	"photo" text
);
--> statement-breakpoint
CREATE TABLE "events_like_by" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"created_at" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"event_id" bigint,
	"user_id" text,
	"like" bigint,
	"usersProfileId" uuid
);
--> statement-breakpoint
CREATE TABLE "event_comment" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"eventId" bigint NOT NULL,
	"userId" text NOT NULL,
	"parentId" bigint,
	"content" text NOT NULL,
	"likeCount" integer DEFAULT 0 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "event_comment_like" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"commentId" bigint NOT NULL,
	"userId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chess_game_round" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"roundNumber" integer NOT NULL,
	"winnerId" text,
	"winnerName" varchar(100),
	"whitePlayerId" text NOT NULL,
	"blackPlayerId" text NOT NULL,
	"result" varchar(20) DEFAULT 'ACTIVE' NOT NULL,
	"gameMode" varchar(50) NOT NULL,
	"timeControl" varchar(50),
	"pgnMoves" text,
	"roundNotes" text,
	"startedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"completedAt" timestamp(3)
);
--> statement-breakpoint
CREATE TABLE "billiard_round_player" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"roundId" bigint NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"playerId" text NOT NULL,
	"playerName" varchar(100) NOT NULL,
	"participationStatus" varchar(20) NOT NULL,
	"betAmount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "event_location_share" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"eventId" bigint NOT NULL,
	"userId" text NOT NULL,
	"latitude" double precision NOT NULL,
	"longitude" double precision NOT NULL,
	"timestamp" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "billiard_game_session" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"komunitasId" bigint NOT NULL,
	"gameName" varchar(200) NOT NULL,
	"gameRules" text,
	"betAmount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"penaltyAmount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"gameDate" timestamp(3) NOT NULL,
	"status" varchar(50) DEFAULT 'ACTIVE' NOT NULL,
	"totalRounds" integer DEFAULT 0 NOT NULL,
	"createdBy" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"gameType" "GameType" DEFAULT 'BILLIARD' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "billiard_game_player" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"playerId" text NOT NULL,
	"playerName" varchar(100) NOT NULL,
	"joinedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "billiard_game_round" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"roundNumber" integer NOT NULL,
	"winnerId" text NOT NULL,
	"winnerName" varchar(100) NOT NULL,
	"status" varchar(50) DEFAULT 'ACTIVE' NOT NULL,
	"roundNotes" text,
	"startedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"completedAt" timestamp(3)
);
--> statement-breakpoint
CREATE TABLE "billiard_transaction" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"roundId" bigint NOT NULL,
	"roundNumber" integer NOT NULL,
	"transactionType" varchar(50) NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"fromPlayerId" text,
	"fromPlayerName" varchar(100),
	"toPlayerId" text,
	"toPlayerName" varchar(100),
	"description" text,
	"faultReason" varchar(500),
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chess_game_player" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"playerId" text NOT NULL,
	"playerName" varchar(100) NOT NULL,
	"eloRating" integer DEFAULT 1200 NOT NULL,
	"color" varchar(10),
	"joinedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chess_game_session" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"komunitasId" bigint NOT NULL,
	"gameType" "GameType" DEFAULT 'CHESS' NOT NULL,
	"gameName" varchar(200) NOT NULL,
	"gameRules" text,
	"betAmount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"penaltyAmount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"gameDate" timestamp(3) NOT NULL,
	"status" varchar(50) DEFAULT 'ACTIVE' NOT NULL,
	"totalRounds" integer DEFAULT 0 NOT NULL,
	"createdBy" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chess_round_player" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"roundId" bigint NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"playerId" text NOT NULL,
	"playerName" varchar(100) NOT NULL,
	"participationStatus" varchar(20) NOT NULL,
	"betAmount" numeric(10, 2) DEFAULT '0' NOT NULL,
	"eloChange" integer DEFAULT 0 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chess_transaction" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"gameSessionId" bigint NOT NULL,
	"roundId" bigint NOT NULL,
	"roundNumber" integer NOT NULL,
	"transactionType" varchar(50) NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"fromPlayerId" text,
	"fromPlayerName" varchar(100),
	"toPlayerId" text,
	"toPlayerName" varchar(100),
	"description" text,
	"faultReason" varchar(500),
	"eloChange" integer DEFAULT 0 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
ALTER TABLE "account" ADD CONSTRAINT "account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "profile" ADD CONSTRAINT "profile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "privacy_setting" ADD CONSTRAINT "privacy_setting_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "notification_preference" ADD CONSTRAINT "notification_preference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "session_info" ADD CONSTRAINT "session_info_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "public"."session"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "security_key" ADD CONSTRAINT "security_key_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "login_history" ADD CONSTRAINT "login_history_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events_participant" ADD CONSTRAINT "events_participant_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events_participant" ADD CONSTRAINT "events_participant_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events_participant" ADD CONSTRAINT "events_participant_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "user_activity" ADD CONSTRAINT "user_activity_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events" ADD CONSTRAINT "events_komunitas_id_fkey" FOREIGN KEY ("komunitas_id") REFERENCES "public"."komunitas"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events" ADD CONSTRAINT "events_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events" ADD CONSTRAINT "events_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events_route" ADD CONSTRAINT "events_route_events_id_fkey" FOREIGN KEY ("events_id") REFERENCES "public"."events"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "EventsRegQuestion" ADD CONSTRAINT "EventsRegQuestion_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_member" ADD CONSTRAINT "komunitas_member_komunitas_id_fkey" FOREIGN KEY ("komunitas_id") REFERENCES "public"."komunitas"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_member" ADD CONSTRAINT "komunitas_member_komunitas_member_fkey" FOREIGN KEY ("komunitas_member") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_member" ADD CONSTRAINT "komunitas_member_member_role_fkey" FOREIGN KEY ("member_role") REFERENCES "public"."mfRoles_Komunitas"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_member" ADD CONSTRAINT "komunitas_member_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "EventsOTW" ADD CONSTRAINT "EventsOTW_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "EventsOTW" ADD CONSTRAINT "EventsOTW_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "EventsOTW" ADD CONSTRAINT "EventsOTW_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_diskusi" ADD CONSTRAINT "komunitas_diskusi_komunitas_id_fkey" FOREIGN KEY ("komunitas_id") REFERENCES "public"."komunitas"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_diskusi" ADD CONSTRAINT "komunitas_diskusi_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_diskusi" ADD CONSTRAINT "komunitas_diskusi_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_diskusi_komentar" ADD CONSTRAINT "komunitas_diskusi_komentar_komunitas_diskusi_id_fkey" FOREIGN KEY ("komunitas_diskusi_id") REFERENCES "public"."komunitas_diskusi"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_diskusi_komentar" ADD CONSTRAINT "komunitas_diskusi_komentar_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_diskusi_komentar" ADD CONSTRAINT "komunitas_diskusi_komentar_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "places" ADD CONSTRAINT "places_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "places" ADD CONSTRAINT "places_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_like_by" ADD CONSTRAINT "komunitas_like_by_komunitas_id_fkey" FOREIGN KEY ("komunitas_id") REFERENCES "public"."komunitas"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_like_by" ADD CONSTRAINT "komunitas_like_by_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "komunitas_like_by" ADD CONSTRAINT "komunitas_like_by_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "session" ADD CONSTRAINT "session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events_like_by" ADD CONSTRAINT "events_like_by_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events_like_by" ADD CONSTRAINT "events_like_by_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "events_like_by" ADD CONSTRAINT "events_like_by_usersProfileId_fkey" FOREIGN KEY ("usersProfileId") REFERENCES "public"."users_profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "event_comment" ADD CONSTRAINT "event_comment_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "public"."events"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "event_comment" ADD CONSTRAINT "event_comment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "event_comment" ADD CONSTRAINT "event_comment_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "public"."event_comment"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "event_comment_like" ADD CONSTRAINT "event_comment_like_commentId_fkey" FOREIGN KEY ("commentId") REFERENCES "public"."event_comment"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "event_comment_like" ADD CONSTRAINT "event_comment_like_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_round" ADD CONSTRAINT "chess_game_round_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."chess_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_round" ADD CONSTRAINT "chess_game_round_winnerId_fkey" FOREIGN KEY ("winnerId") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_round" ADD CONSTRAINT "chess_game_round_whitePlayerId_fkey" FOREIGN KEY ("whitePlayerId") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_round" ADD CONSTRAINT "chess_game_round_blackPlayerId_fkey" FOREIGN KEY ("blackPlayerId") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_round_player" ADD CONSTRAINT "billiard_round_player_roundId_fkey" FOREIGN KEY ("roundId") REFERENCES "public"."billiard_game_round"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_round_player" ADD CONSTRAINT "billiard_round_player_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."billiard_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_round_player" ADD CONSTRAINT "billiard_round_player_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "event_location_share" ADD CONSTRAINT "event_location_share_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "public"."events"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "event_location_share" ADD CONSTRAINT "event_location_share_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_game_session" ADD CONSTRAINT "billiard_game_session_komunitasId_fkey" FOREIGN KEY ("komunitasId") REFERENCES "public"."komunitas"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_game_session" ADD CONSTRAINT "billiard_game_session_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_game_player" ADD CONSTRAINT "billiard_game_player_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."billiard_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_game_player" ADD CONSTRAINT "billiard_game_player_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_game_round" ADD CONSTRAINT "billiard_game_round_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."billiard_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_game_round" ADD CONSTRAINT "billiard_game_round_winnerId_fkey" FOREIGN KEY ("winnerId") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_transaction" ADD CONSTRAINT "billiard_transaction_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."billiard_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_transaction" ADD CONSTRAINT "billiard_transaction_roundId_fkey" FOREIGN KEY ("roundId") REFERENCES "public"."billiard_game_round"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_transaction" ADD CONSTRAINT "billiard_transaction_fromPlayerId_fkey" FOREIGN KEY ("fromPlayerId") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "billiard_transaction" ADD CONSTRAINT "billiard_transaction_toPlayerId_fkey" FOREIGN KEY ("toPlayerId") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_player" ADD CONSTRAINT "chess_game_player_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."chess_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_player" ADD CONSTRAINT "chess_game_player_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_session" ADD CONSTRAINT "chess_game_session_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_game_session" ADD CONSTRAINT "chess_game_session_komunitasId_fkey" FOREIGN KEY ("komunitasId") REFERENCES "public"."komunitas"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_round_player" ADD CONSTRAINT "chess_round_player_roundId_fkey" FOREIGN KEY ("roundId") REFERENCES "public"."chess_game_round"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_round_player" ADD CONSTRAINT "chess_round_player_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."chess_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_round_player" ADD CONSTRAINT "chess_round_player_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "public"."profile"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_transaction" ADD CONSTRAINT "chess_transaction_fromPlayerId_fkey" FOREIGN KEY ("fromPlayerId") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_transaction" ADD CONSTRAINT "chess_transaction_gameSessionId_fkey" FOREIGN KEY ("gameSessionId") REFERENCES "public"."chess_game_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_transaction" ADD CONSTRAINT "chess_transaction_roundId_fkey" FOREIGN KEY ("roundId") REFERENCES "public"."chess_game_round"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chess_transaction" ADD CONSTRAINT "chess_transaction_toPlayerId_fkey" FOREIGN KEY ("toPlayerId") REFERENCES "public"."profile"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
CREATE UNIQUE INDEX "account_providerId_accountId_key" ON "account" USING btree ("providerId" text_ops,"accountId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "profile_userId_key" ON "profile" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "profile_username_key" ON "profile" USING btree ("username" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "privacy_setting_userId_key" ON "privacy_setting" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "verification_identifier_value_key" ON "verification" USING btree ("identifier" text_ops,"value" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "notification_preference_userId_key" ON "notification_preference" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "session_info_sessionId_key" ON "session_info" USING btree ("sessionId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "security_key_keyId_key" ON "security_key" USING btree ("keyId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "user_email_key" ON "user" USING btree ("email" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "session_token_key" ON "session" USING btree ("token" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "event_comment_like_commentId_userId_key" ON "event_comment_like" USING btree ("commentId" int8_ops,"userId" int8_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "chess_game_round_gameSessionId_roundNumber_key" ON "chess_game_round" USING btree ("gameSessionId" int4_ops,"roundNumber" int4_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "billiard_round_player_roundId_playerId_key" ON "billiard_round_player" USING btree ("roundId" int8_ops,"playerId" int8_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "event_location_share_eventId_userId_key" ON "event_location_share" USING btree ("eventId" int8_ops,"userId" int8_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "billiard_game_player_gameSessionId_playerId_key" ON "billiard_game_player" USING btree ("gameSessionId" int8_ops,"playerId" int8_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "billiard_game_round_gameSessionId_roundNumber_key" ON "billiard_game_round" USING btree ("gameSessionId" int4_ops,"roundNumber" int4_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "chess_game_player_gameSessionId_playerId_key" ON "chess_game_player" USING btree ("gameSessionId" int8_ops,"playerId" int8_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "chess_round_player_roundId_playerId_key" ON "chess_round_player" USING btree ("roundId" int8_ops,"playerId" int8_ops);
*/