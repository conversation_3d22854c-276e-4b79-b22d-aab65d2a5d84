import { pgTable, uniqueIndex, foreignKey, text, timestamp, integer, boolean, bigserial, bigint, smallint, uuid, jsonb, date, doublePrecision, varchar, numeric, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const fieldVisibility = pgEnum("FieldVisibility", ['PUBLIC', 'PRIVATE', 'HIDDEN'])
export const gameType = pgEnum("GameType", ['BILLIARD', 'CHESS'])
export const notificationFrequency = pgEnum("NotificationFrequency", ['REALTIME', 'DAILY', 'WEEKLY', 'MONTHLY'])
export const profileVisibility = pgEnum("ProfileVisibility", ['PUBLIC', 'PRIVATE', 'REGISTERED_USERS_ONLY'])


export const account = pgTable("account", {
	id: text().primaryKey().notNull(),
	accountId: text().notNull(),
	providerId: text().notNull(),
	userId: text().notNull(),
	accessToken: text(),
	refreshToken: text(),
	idToken: text(),
	accessTokenExpiresAt: timestamp({ precision: 3, mode: 'string' }),
	refreshTokenExpiresAt: timestamp({ precision: 3, mode: 'string' }),
	scope: text(),
	password: text(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	uniqueIndex("account_providerId_accountId_key").using("btree", table.providerId.asc().nullsLast().op("text_ops"), table.accountId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "account_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const profile = pgTable("profile", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	firstName: text(),
	lastName: text(),
	username: text(),
	phone: text(),
	bio: text(),
	dateOfBirth: timestamp({ precision: 3, mode: 'string' }),
	location: text(),
	jobTitle: text(),
	website: text(),
	timezone: text().default('UTC'),
	language: text().default('en'),
	fullName: text("full_name"),
	initial: text(),
	gender: text(),
	photo: text(),
	profileCompleteness: integer().default(0).notNull(),
}, (table) => [
	uniqueIndex("profile_userId_key").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	uniqueIndex("profile_username_key").using("btree", table.username.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "profile_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const privacySetting = pgTable("privacy_setting", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	profileVisibility: profileVisibility().default('PRIVATE').notNull(),
	emailVisibility: fieldVisibility().default('PRIVATE').notNull(),
	phoneVisibility: fieldVisibility().default('PRIVATE').notNull(),
	locationVisibility: fieldVisibility().default('PRIVATE').notNull(),
	lastSeenVisibility: fieldVisibility().default('PRIVATE').notNull(),
}, (table) => [
	uniqueIndex("privacy_setting_userId_key").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "privacy_setting_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const verification = pgTable("verification", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	uniqueIndex("verification_identifier_value_key").using("btree", table.identifier.asc().nullsLast().op("text_ops"), table.value.asc().nullsLast().op("text_ops")),
]);

export const notificationPreference = pgTable("notification_preference", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	emailNotifications: boolean().default(true).notNull(),
	securityAlerts: boolean().default(true).notNull(),
	accountUpdates: boolean().default(true).notNull(),
	productUpdates: boolean().default(false).notNull(),
	marketingEmails: boolean().default(false).notNull(),
	smsNotifications: boolean().default(false).notNull(),
	notificationFrequency: notificationFrequency().default('REALTIME').notNull(),
}, (table) => [
	uniqueIndex("notification_preference_userId_key").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "notification_preference_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const sessionInfo = pgTable("session_info", {
	id: text().primaryKey().notNull(),
	sessionId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	deviceType: text(),
	browser: text(),
	operatingSystem: text(),
	location: text(),
	country: text(),
	city: text(),
	nickname: text(),
	isTrusted: boolean().default(false).notNull(),
}, (table) => [
	uniqueIndex("session_info_sessionId_key").using("btree", table.sessionId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.sessionId],
			foreignColumns: [session.id],
			name: "session_info_sessionId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const securityKey = pgTable("security_key", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	name: text().notNull(),
	keyId: text().notNull(),
	publicKey: text().notNull(),
	counter: integer().default(0).notNull(),
	lastUsed: timestamp({ precision: 3, mode: 'string' }),
}, (table) => [
	uniqueIndex("security_key_keyId_key").using("btree", table.keyId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "security_key_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const loginHistory = pgTable("login_history", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	ipAddress: text(),
	userAgent: text(),
	deviceType: text(),
	browser: text(),
	operatingSystem: text(),
	location: text(),
	country: text(),
	city: text(),
	success: boolean().default(true).notNull(),
	failureReason: text(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "login_history_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	emailVerified: boolean().default(false).notNull(),
	image: text(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	twoFactorEnabled: boolean().default(false).notNull(),
}, (table) => [
	uniqueIndex("user_email_key").using("btree", table.email.asc().nullsLast().op("text_ops")),
]);

export const eventsParticipant = pgTable("events_participant", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	eventId: bigint("event_id", { mode: "number" }),
	userId: text("user_id"),
	totalPax: smallint("total_pax"),
	status: text(),
	ticketQr: text("ticket_qr"),
	paymentStatus: text("payment_status"),
	adminStatus: boolean("admin_status"),
	ownerStatus: boolean("owner_status"),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "events_participant_event_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "events_participant_user_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "events_participant_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const userActivity = pgTable("user_activity", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	action: text().notNull(),
	description: text(),
	ipAddress: text(),
	userAgent: text(),
	metadata: jsonb(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "user_activity_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const events = pgTable("events", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	public: boolean(),
	name: text(),
	name2: text(),
	description: text(),
	startDate: date("start_date"),
	endDate: date("end_date"),
	status: text(),
	location: text(),
	cordinate: text(),
	eventType: text("event_type"),
	createdBy: text("created_by"),
	registrationCloseDate: date("registration_close_date"),
	donasiSingle: doublePrecision("donasi_single").default(0).notNull(),
	donasiCouple: doublePrecision("donasi_couple").default(0).notNull(),
	donasiCustom: doublePrecision("donasi_custom").default(0).notNull(),
	donasiInfo: text("donasi_info"),
	banner: text(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	komunitasId: bigint("komunitas_id", { mode: "number" }),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.komunitasId],
			foreignColumns: [komunitas.id],
			name: "events_komunitas_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [profile.id],
			name: "events_created_by_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "events_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const eventsRoute = pgTable("events_route", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	eventsId: bigint("events_id", { mode: "number" }),
	grouping: text(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	routeOrder: bigint("route_order", { mode: "number" }),
	name: text(),
	point: text(),
	description: text(),
	waktu: timestamp({ precision: 6, mode: 'string' }),
	cordinate: text(),
	tanggal: date(),
}, (table) => [
	foreignKey({
			columns: [table.eventsId],
			foreignColumns: [events.id],
			name: "events_route_events_id_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const eventsRegQuestion = pgTable("EventsRegQuestion", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	eventId: bigint("event_id", { mode: "number" }),
	question: text(),
	answerType: text("answer_type"),
	answerChoice: text("answer_choice"),
	urut: smallint(),
}, (table) => [
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "EventsRegQuestion_event_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const komunitasMember = pgTable("komunitas_member", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	komunitasId: bigint("komunitas_id", { mode: "number" }),
	komunitasMember: text("komunitas_member"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	memberRole: bigint("member_role", { mode: "number" }),
	status: text(),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.komunitasId],
			foreignColumns: [komunitas.id],
			name: "komunitas_member_komunitas_id_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.komunitasMember],
			foreignColumns: [profile.id],
			name: "komunitas_member_komunitas_member_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.memberRole],
			foreignColumns: [mfRolesKomunitas.id],
			name: "komunitas_member_member_role_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "komunitas_member_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const eventsOtw = pgTable("EventsOTW", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	eventId: bigint("event_id", { mode: "number" }),
	userId: text("user_id"),
	status: text(),
	waktuOtw: timestamp("waktu_otw", { precision: 6, mode: 'string' }),
	waktuUpdate: timestamp("waktu_update", { precision: 6, mode: 'string' }),
	lokasiOtw: text("lokasi_otw"),
	lokasiUpdate: text("lokasi_update"),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "EventsOTW_event_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "EventsOTW_user_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "EventsOTW_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const komunitasDiskusi = pgTable("komunitas_diskusi", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	komunitasId: bigint("komunitas_id", { mode: "number" }),
	topik: text(),
	image: text(),
	like: smallint(),
	status: text(),
	userId: text("user_id"),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.komunitasId],
			foreignColumns: [komunitas.id],
			name: "komunitas_diskusi_komunitas_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "komunitas_diskusi_user_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "komunitas_diskusi_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const komunitasDiskusiKomentar = pgTable("komunitas_diskusi_komentar", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	komunitasDiskusiId: bigint("komunitas_diskusi_id", { mode: "number" }),
	komentar: text(),
	image: text(),
	like: integer(),
	userId: text("user_id"),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.komunitasDiskusiId],
			foreignColumns: [komunitasDiskusi.id],
			name: "komunitas_diskusi_komentar_komunitas_diskusi_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "komunitas_diskusi_komentar_user_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "komunitas_diskusi_komentar_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const komunitas = pgTable("komunitas", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	nama: text(),
	description: text(),
	type: text().default('),
	since: timestamp({ precision: 6, mode: 'string' }),
	founder: text(),
	shortTitle: text("short_title").default('),
	nama2: text(),
	status: text().default('),
	join: text().default('),
	profileImage: text("profile_Image"),
	bannerImage: text("banner_image"),
});

export const places = pgTable("places", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	userId: text("user_id"),
	name: text(),
	area: text(),
	deskripsi: text(),
	latitude: text(),
	longitude: text(),
	image: text(),
	shortDes: text("short_des"),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "places_user_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "places_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const mfRolesKomunitas = pgTable("mfRoles_Komunitas", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	role: text().default('15'),
});

export const komunitasLikeBy = pgTable("komunitas_like_by", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	komunitasId: bigint("komunitas_id", { mode: "number" }),
	userId: text("user_id"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	like: bigint({ mode: "number" }),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.komunitasId],
			foreignColumns: [komunitas.id],
			name: "komunitas_like_by_komunitas_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "komunitas_like_by_user_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "komunitas_like_by_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const session = pgTable("session", {
	id: text().primaryKey().notNull(),
	expiresAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	token: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	ipAddress: text(),
	userAgent: text(),
	userId: text().notNull(),
}, (table) => [
	uniqueIndex("session_token_key").using("btree", table.token.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "session_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const usersProfile = pgTable("users_profile", {
	id: uuid().primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	fullName: text("full_name"),
	initial: text(),
	tglLahir: date("tgl_lahir"),
	gender: text(),
	photo: text(),
});

export const eventsLikeBy = pgTable("events_like_by", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	eventId: bigint("event_id", { mode: "number" }),
	userId: text("user_id"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	like: bigint({ mode: "number" }),
	usersProfileId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "events_like_by_event_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "events_like_by_user_id_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.usersProfileId],
			foreignColumns: [usersProfile.id],
			name: "events_like_by_usersProfileId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const eventComment = pgTable("event_comment", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	eventId: bigint({ mode: "number" }).notNull(),
	userId: text().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	parentId: bigint({ mode: "number" }),
	content: text().notNull(),
	likeCount: integer().default(0).notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "event_comment_eventId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "event_comment_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.parentId],
			foreignColumns: [table.id],
			name: "event_comment_parentId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const eventCommentLike = pgTable("event_comment_like", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	commentId: bigint({ mode: "number" }).notNull(),
	userId: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	uniqueIndex("event_comment_like_commentId_userId_key").using("btree", table.commentId.asc().nullsLast().op("int8_ops"), table.userId.asc().nullsLast().op("int8_ops")),
	foreignKey({
			columns: [table.commentId],
			foreignColumns: [eventComment.id],
			name: "event_comment_like_commentId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "event_comment_like_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const chessGameRound = pgTable("chess_game_round", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	roundNumber: integer().notNull(),
	winnerId: text(),
	winnerName: varchar({ length: 100 }),
	whitePlayerId: text().notNull(),
	blackPlayerId: text().notNull(),
	result: varchar({ length: 20 }).default('ACTIVE').notNull(),
	gameMode: varchar({ length: 50 }).notNull(),
	timeControl: varchar({ length: 50 }),
	pgnMoves: text(),
	roundNotes: text(),
	startedAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	completedAt: timestamp({ precision: 3, mode: 'string' }),
}, (table) => [
	uniqueIndex("chess_game_round_gameSessionId_roundNumber_key").using("btree", table.gameSessionId.asc().nullsLast().op("int4_ops"), table.roundNumber.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [chessGameSession.id],
			name: "chess_game_round_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.winnerId],
			foreignColumns: [profile.id],
			name: "chess_game_round_winnerId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.whitePlayerId],
			foreignColumns: [profile.id],
			name: "chess_game_round_whitePlayerId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.blackPlayerId],
			foreignColumns: [profile.id],
			name: "chess_game_round_blackPlayerId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const billiardRoundPlayer = pgTable("billiard_round_player", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	roundId: bigint({ mode: "number" }).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	playerId: text().notNull(),
	playerName: varchar({ length: 100 }).notNull(),
	participationStatus: varchar({ length: 20 }).notNull(),
	betAmount: numeric({ precision: 10, scale:  2 }).default('0').notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	uniqueIndex("billiard_round_player_roundId_playerId_key").using("btree", table.roundId.asc().nullsLast().op("int8_ops"), table.playerId.asc().nullsLast().op("int8_ops")),
	foreignKey({
			columns: [table.roundId],
			foreignColumns: [billiardGameRound.id],
			name: "billiard_round_player_roundId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [billiardGameSession.id],
			name: "billiard_round_player_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.playerId],
			foreignColumns: [profile.id],
			name: "billiard_round_player_playerId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const eventLocationShare = pgTable("event_location_share", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	eventId: bigint({ mode: "number" }).notNull(),
	userId: text().notNull(),
	latitude: doublePrecision().notNull(),
	longitude: doublePrecision().notNull(),
	timestamp: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	uniqueIndex("event_location_share_eventId_userId_key").using("btree", table.eventId.asc().nullsLast().op("int8_ops"), table.userId.asc().nullsLast().op("int8_ops")),
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "event_location_share_eventId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [profile.id],
			name: "event_location_share_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const billiardGameSession = pgTable("billiard_game_session", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	komunitasId: bigint({ mode: "number" }).notNull(),
	gameName: varchar({ length: 200 }).notNull(),
	gameRules: text(),
	betAmount: numeric({ precision: 10, scale:  2 }).default('0').notNull(),
	penaltyAmount: numeric({ precision: 10, scale:  2 }).default('0').notNull(),
	gameDate: timestamp({ precision: 3, mode: 'string' }).notNull(),
	status: varchar({ length: 50 }).default('ACTIVE').notNull(),
	totalRounds: integer().default(0).notNull(),
	createdBy: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	gameType: gameType().default('BILLIARD').notNull(),
}, (table) => [
	foreignKey({
			columns: [table.komunitasId],
			foreignColumns: [komunitas.id],
			name: "billiard_game_session_komunitasId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [profile.id],
			name: "billiard_game_session_createdBy_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const billiardGamePlayer = pgTable("billiard_game_player", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	playerId: text().notNull(),
	playerName: varchar({ length: 100 }).notNull(),
	joinedAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	uniqueIndex("billiard_game_player_gameSessionId_playerId_key").using("btree", table.gameSessionId.asc().nullsLast().op("int8_ops"), table.playerId.asc().nullsLast().op("int8_ops")),
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [billiardGameSession.id],
			name: "billiard_game_player_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.playerId],
			foreignColumns: [profile.id],
			name: "billiard_game_player_playerId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const billiardGameRound = pgTable("billiard_game_round", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	roundNumber: integer().notNull(),
	winnerId: text().notNull(),
	winnerName: varchar({ length: 100 }).notNull(),
	status: varchar({ length: 50 }).default('ACTIVE').notNull(),
	roundNotes: text(),
	startedAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	completedAt: timestamp({ precision: 3, mode: 'string' }),
}, (table) => [
	uniqueIndex("billiard_game_round_gameSessionId_roundNumber_key").using("btree", table.gameSessionId.asc().nullsLast().op("int4_ops"), table.roundNumber.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [billiardGameSession.id],
			name: "billiard_game_round_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.winnerId],
			foreignColumns: [profile.id],
			name: "billiard_game_round_winnerId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const billiardTransaction = pgTable("billiard_transaction", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	roundId: bigint({ mode: "number" }).notNull(),
	roundNumber: integer().notNull(),
	transactionType: varchar({ length: 50 }).notNull(),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	fromPlayerId: text(),
	fromPlayerName: varchar({ length: 100 }),
	toPlayerId: text(),
	toPlayerName: varchar({ length: 100 }),
	description: text(),
	faultReason: varchar({ length: 500 }),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [billiardGameSession.id],
			name: "billiard_transaction_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.roundId],
			foreignColumns: [billiardGameRound.id],
			name: "billiard_transaction_roundId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.fromPlayerId],
			foreignColumns: [profile.id],
			name: "billiard_transaction_fromPlayerId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.toPlayerId],
			foreignColumns: [profile.id],
			name: "billiard_transaction_toPlayerId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const chessGamePlayer = pgTable("chess_game_player", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	playerId: text().notNull(),
	playerName: varchar({ length: 100 }).notNull(),
	eloRating: integer().default(1200).notNull(),
	color: varchar({ length: 10 }),
	joinedAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	uniqueIndex("chess_game_player_gameSessionId_playerId_key").using("btree", table.gameSessionId.asc().nullsLast().op("int8_ops"), table.playerId.asc().nullsLast().op("int8_ops")),
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [chessGameSession.id],
			name: "chess_game_player_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.playerId],
			foreignColumns: [profile.id],
			name: "chess_game_player_playerId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const chessGameSession = pgTable("chess_game_session", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	komunitasId: bigint({ mode: "number" }).notNull(),
	gameType: gameType().default('CHESS').notNull(),
	gameName: varchar({ length: 200 }).notNull(),
	gameRules: text(),
	betAmount: numeric({ precision: 10, scale:  2 }).default('0').notNull(),
	penaltyAmount: numeric({ precision: 10, scale:  2 }).default('0').notNull(),
	gameDate: timestamp({ precision: 3, mode: 'string' }).notNull(),
	status: varchar({ length: 50 }).default('ACTIVE').notNull(),
	totalRounds: integer().default(0).notNull(),
	createdBy: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [profile.id],
			name: "chess_game_session_createdBy_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.komunitasId],
			foreignColumns: [komunitas.id],
			name: "chess_game_session_komunitasId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const chessRoundPlayer = pgTable("chess_round_player", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	roundId: bigint({ mode: "number" }).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	playerId: text().notNull(),
	playerName: varchar({ length: 100 }).notNull(),
	participationStatus: varchar({ length: 20 }).notNull(),
	betAmount: numeric({ precision: 10, scale:  2 }).default('0').notNull(),
	eloChange: integer().default(0).notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	uniqueIndex("chess_round_player_roundId_playerId_key").using("btree", table.roundId.asc().nullsLast().op("int8_ops"), table.playerId.asc().nullsLast().op("int8_ops")),
	foreignKey({
			columns: [table.roundId],
			foreignColumns: [chessGameRound.id],
			name: "chess_round_player_roundId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [chessGameSession.id],
			name: "chess_round_player_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.playerId],
			foreignColumns: [profile.id],
			name: "chess_round_player_playerId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
]);

export const chessTransaction = pgTable("chess_transaction", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	gameSessionId: bigint({ mode: "number" }).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	roundId: bigint({ mode: "number" }).notNull(),
	roundNumber: integer().notNull(),
	transactionType: varchar({ length: 50 }).notNull(),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	fromPlayerId: text(),
	fromPlayerName: varchar({ length: 100 }),
	toPlayerId: text(),
	toPlayerName: varchar({ length: 100 }),
	description: text(),
	faultReason: varchar({ length: 500 }),
	eloChange: integer().default(0).notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.fromPlayerId],
			foreignColumns: [profile.id],
			name: "chess_transaction_fromPlayerId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
	foreignKey({
			columns: [table.gameSessionId],
			foreignColumns: [chessGameSession.id],
			name: "chess_transaction_gameSessionId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.roundId],
			foreignColumns: [chessGameRound.id],
			name: "chess_transaction_roundId_fkey"
		}).onUpdate("cascade").onDelete("restrict"),
	foreignKey({
			columns: [table.toPlayerId],
			foreignColumns: [profile.id],
			name: "chess_transaction_toPlayerId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);
