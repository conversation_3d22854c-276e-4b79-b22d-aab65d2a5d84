import { relations } from "drizzle-orm/relations";
import { user, account, profile, privacySetting, notificationPreference, session, sessionInfo, securityKey, loginHistory, events, eventsParticipant, usersProfile, userActivity, komunitas, eventsRoute, eventsRegQuestion, komunitasMember, mfRolesKomunitas, eventsOtw, komunitasDiskusi, komunitasDiskusiKomentar, places, komunitasLikeBy, eventsLikeBy, eventComment, eventCommentLike, chessGameSession, chessGameRound, billiardGameRound, billiardRoundPlayer, billiardGameSession, eventLocationShare, billiardGamePlayer, billiardTransaction, chessGamePlayer, chessRoundPlayer, chessTransaction } from "./schema";

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	accounts: many(account),
	profiles: many(profile),
	privacySettings: many(privacySetting),
	notificationPreferences: many(notificationPreference),
	securityKeys: many(securityKey),
	loginHistories: many(loginHistory),
	userActivities: many(userActivity),
	sessions: many(session),
	eventComments: many(eventComment),
	eventCommentLikes: many(eventCommentLike),
}));

export const profileRelations = relations(profile, ({one, many}) => ({
	user: one(user, {
		fields: [profile.userId],
		references: [user.id]
	}),
	eventsParticipants: many(eventsParticipant),
	events: many(events),
	komunitasMembers: many(komunitasMember),
	eventsOtws: many(eventsOtw),
	komunitasDiskusis: many(komunitasDiskusi),
	komunitasDiskusiKomentars: many(komunitasDiskusiKomentar),
	places: many(places),
	komunitasLikeBies: many(komunitasLikeBy),
	eventsLikeBies: many(eventsLikeBy),
	chessGameRounds_winnerId: many(chessGameRound, {
		relationName: "chessGameRound_winnerId_profile_id"
	}),
	chessGameRounds_whitePlayerId: many(chessGameRound, {
		relationName: "chessGameRound_whitePlayerId_profile_id"
	}),
	chessGameRounds_blackPlayerId: many(chessGameRound, {
		relationName: "chessGameRound_blackPlayerId_profile_id"
	}),
	billiardRoundPlayers: many(billiardRoundPlayer),
	eventLocationShares: many(eventLocationShare),
	billiardGameSessions: many(billiardGameSession),
	billiardGamePlayers: many(billiardGamePlayer),
	billiardGameRounds: many(billiardGameRound),
	billiardTransactions_fromPlayerId: many(billiardTransaction, {
		relationName: "billiardTransaction_fromPlayerId_profile_id"
	}),
	billiardTransactions_toPlayerId: many(billiardTransaction, {
		relationName: "billiardTransaction_toPlayerId_profile_id"
	}),
	chessGamePlayers: many(chessGamePlayer),
	chessGameSessions: many(chessGameSession),
	chessRoundPlayers: many(chessRoundPlayer),
	chessTransactions_fromPlayerId: many(chessTransaction, {
		relationName: "chessTransaction_fromPlayerId_profile_id"
	}),
	chessTransactions_toPlayerId: many(chessTransaction, {
		relationName: "chessTransaction_toPlayerId_profile_id"
	}),
}));

export const privacySettingRelations = relations(privacySetting, ({one}) => ({
	user: one(user, {
		fields: [privacySetting.userId],
		references: [user.id]
	}),
}));

export const notificationPreferenceRelations = relations(notificationPreference, ({one}) => ({
	user: one(user, {
		fields: [notificationPreference.userId],
		references: [user.id]
	}),
}));

export const sessionInfoRelations = relations(sessionInfo, ({one}) => ({
	session: one(session, {
		fields: [sessionInfo.sessionId],
		references: [session.id]
	}),
}));

export const sessionRelations = relations(session, ({one, many}) => ({
	sessionInfos: many(sessionInfo),
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const securityKeyRelations = relations(securityKey, ({one}) => ({
	user: one(user, {
		fields: [securityKey.userId],
		references: [user.id]
	}),
}));

export const loginHistoryRelations = relations(loginHistory, ({one}) => ({
	user: one(user, {
		fields: [loginHistory.userId],
		references: [user.id]
	}),
}));

export const eventsParticipantRelations = relations(eventsParticipant, ({one}) => ({
	event: one(events, {
		fields: [eventsParticipant.eventId],
		references: [events.id]
	}),
	profile: one(profile, {
		fields: [eventsParticipant.userId],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [eventsParticipant.usersProfileId],
		references: [usersProfile.id]
	}),
}));

export const eventsRelations = relations(events, ({one, many}) => ({
	eventsParticipants: many(eventsParticipant),
	komunita: one(komunitas, {
		fields: [events.komunitasId],
		references: [komunitas.id]
	}),
	profile: one(profile, {
		fields: [events.createdBy],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [events.usersProfileId],
		references: [usersProfile.id]
	}),
	eventsRoutes: many(eventsRoute),
	eventsRegQuestions: many(eventsRegQuestion),
	eventsOtws: many(eventsOtw),
	eventsLikeBies: many(eventsLikeBy),
	eventComments: many(eventComment),
	eventLocationShares: many(eventLocationShare),
}));

export const usersProfileRelations = relations(usersProfile, ({many}) => ({
	eventsParticipants: many(eventsParticipant),
	events: many(events),
	komunitasMembers: many(komunitasMember),
	eventsOtws: many(eventsOtw),
	komunitasDiskusis: many(komunitasDiskusi),
	komunitasDiskusiKomentars: many(komunitasDiskusiKomentar),
	places: many(places),
	komunitasLikeBies: many(komunitasLikeBy),
	eventsLikeBies: many(eventsLikeBy),
}));

export const userActivityRelations = relations(userActivity, ({one}) => ({
	user: one(user, {
		fields: [userActivity.userId],
		references: [user.id]
	}),
}));

export const komunitasRelations = relations(komunitas, ({many}) => ({
	events: many(events),
	komunitasMembers: many(komunitasMember),
	komunitasDiskusis: many(komunitasDiskusi),
	komunitasLikeBies: many(komunitasLikeBy),
	billiardGameSessions: many(billiardGameSession),
	chessGameSessions: many(chessGameSession),
}));

export const eventsRouteRelations = relations(eventsRoute, ({one}) => ({
	event: one(events, {
		fields: [eventsRoute.eventsId],
		references: [events.id]
	}),
}));

export const eventsRegQuestionRelations = relations(eventsRegQuestion, ({one}) => ({
	event: one(events, {
		fields: [eventsRegQuestion.eventId],
		references: [events.id]
	}),
}));

export const komunitasMemberRelations = relations(komunitasMember, ({one}) => ({
	komunita: one(komunitas, {
		fields: [komunitasMember.komunitasId],
		references: [komunitas.id]
	}),
	profile: one(profile, {
		fields: [komunitasMember.komunitasMember],
		references: [profile.id]
	}),
	mfRolesKomunita: one(mfRolesKomunitas, {
		fields: [komunitasMember.memberRole],
		references: [mfRolesKomunitas.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [komunitasMember.usersProfileId],
		references: [usersProfile.id]
	}),
}));

export const mfRolesKomunitasRelations = relations(mfRolesKomunitas, ({many}) => ({
	komunitasMembers: many(komunitasMember),
}));

export const eventsOtwRelations = relations(eventsOtw, ({one}) => ({
	event: one(events, {
		fields: [eventsOtw.eventId],
		references: [events.id]
	}),
	profile: one(profile, {
		fields: [eventsOtw.userId],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [eventsOtw.usersProfileId],
		references: [usersProfile.id]
	}),
}));

export const komunitasDiskusiRelations = relations(komunitasDiskusi, ({one, many}) => ({
	komunita: one(komunitas, {
		fields: [komunitasDiskusi.komunitasId],
		references: [komunitas.id]
	}),
	profile: one(profile, {
		fields: [komunitasDiskusi.userId],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [komunitasDiskusi.usersProfileId],
		references: [usersProfile.id]
	}),
	komunitasDiskusiKomentars: many(komunitasDiskusiKomentar),
}));

export const komunitasDiskusiKomentarRelations = relations(komunitasDiskusiKomentar, ({one}) => ({
	komunitasDiskusi: one(komunitasDiskusi, {
		fields: [komunitasDiskusiKomentar.komunitasDiskusiId],
		references: [komunitasDiskusi.id]
	}),
	profile: one(profile, {
		fields: [komunitasDiskusiKomentar.userId],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [komunitasDiskusiKomentar.usersProfileId],
		references: [usersProfile.id]
	}),
}));

export const placesRelations = relations(places, ({one}) => ({
	profile: one(profile, {
		fields: [places.userId],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [places.usersProfileId],
		references: [usersProfile.id]
	}),
}));

export const komunitasLikeByRelations = relations(komunitasLikeBy, ({one}) => ({
	komunita: one(komunitas, {
		fields: [komunitasLikeBy.komunitasId],
		references: [komunitas.id]
	}),
	profile: one(profile, {
		fields: [komunitasLikeBy.userId],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [komunitasLikeBy.usersProfileId],
		references: [usersProfile.id]
	}),
}));

export const eventsLikeByRelations = relations(eventsLikeBy, ({one}) => ({
	event: one(events, {
		fields: [eventsLikeBy.eventId],
		references: [events.id]
	}),
	profile: one(profile, {
		fields: [eventsLikeBy.userId],
		references: [profile.id]
	}),
	usersProfile: one(usersProfile, {
		fields: [eventsLikeBy.usersProfileId],
		references: [usersProfile.id]
	}),
}));

export const eventCommentRelations = relations(eventComment, ({one, many}) => ({
	event: one(events, {
		fields: [eventComment.eventId],
		references: [events.id]
	}),
	user: one(user, {
		fields: [eventComment.userId],
		references: [user.id]
	}),
	eventComment: one(eventComment, {
		fields: [eventComment.parentId],
		references: [eventComment.id],
		relationName: "eventComment_parentId_eventComment_id"
	}),
	eventComments: many(eventComment, {
		relationName: "eventComment_parentId_eventComment_id"
	}),
	eventCommentLikes: many(eventCommentLike),
}));

export const eventCommentLikeRelations = relations(eventCommentLike, ({one}) => ({
	eventComment: one(eventComment, {
		fields: [eventCommentLike.commentId],
		references: [eventComment.id]
	}),
	user: one(user, {
		fields: [eventCommentLike.userId],
		references: [user.id]
	}),
}));

export const chessGameRoundRelations = relations(chessGameRound, ({one, many}) => ({
	chessGameSession: one(chessGameSession, {
		fields: [chessGameRound.gameSessionId],
		references: [chessGameSession.id]
	}),
	profile_winnerId: one(profile, {
		fields: [chessGameRound.winnerId],
		references: [profile.id],
		relationName: "chessGameRound_winnerId_profile_id"
	}),
	profile_whitePlayerId: one(profile, {
		fields: [chessGameRound.whitePlayerId],
		references: [profile.id],
		relationName: "chessGameRound_whitePlayerId_profile_id"
	}),
	profile_blackPlayerId: one(profile, {
		fields: [chessGameRound.blackPlayerId],
		references: [profile.id],
		relationName: "chessGameRound_blackPlayerId_profile_id"
	}),
	chessRoundPlayers: many(chessRoundPlayer),
	chessTransactions: many(chessTransaction),
}));

export const chessGameSessionRelations = relations(chessGameSession, ({one, many}) => ({
	chessGameRounds: many(chessGameRound),
	chessGamePlayers: many(chessGamePlayer),
	profile: one(profile, {
		fields: [chessGameSession.createdBy],
		references: [profile.id]
	}),
	komunita: one(komunitas, {
		fields: [chessGameSession.komunitasId],
		references: [komunitas.id]
	}),
	chessRoundPlayers: many(chessRoundPlayer),
	chessTransactions: many(chessTransaction),
}));

export const billiardRoundPlayerRelations = relations(billiardRoundPlayer, ({one}) => ({
	billiardGameRound: one(billiardGameRound, {
		fields: [billiardRoundPlayer.roundId],
		references: [billiardGameRound.id]
	}),
	billiardGameSession: one(billiardGameSession, {
		fields: [billiardRoundPlayer.gameSessionId],
		references: [billiardGameSession.id]
	}),
	profile: one(profile, {
		fields: [billiardRoundPlayer.playerId],
		references: [profile.id]
	}),
}));

export const billiardGameRoundRelations = relations(billiardGameRound, ({one, many}) => ({
	billiardRoundPlayers: many(billiardRoundPlayer),
	billiardGameSession: one(billiardGameSession, {
		fields: [billiardGameRound.gameSessionId],
		references: [billiardGameSession.id]
	}),
	profile: one(profile, {
		fields: [billiardGameRound.winnerId],
		references: [profile.id]
	}),
	billiardTransactions: many(billiardTransaction),
}));

export const billiardGameSessionRelations = relations(billiardGameSession, ({one, many}) => ({
	billiardRoundPlayers: many(billiardRoundPlayer),
	komunita: one(komunitas, {
		fields: [billiardGameSession.komunitasId],
		references: [komunitas.id]
	}),
	profile: one(profile, {
		fields: [billiardGameSession.createdBy],
		references: [profile.id]
	}),
	billiardGamePlayers: many(billiardGamePlayer),
	billiardGameRounds: many(billiardGameRound),
	billiardTransactions: many(billiardTransaction),
}));

export const eventLocationShareRelations = relations(eventLocationShare, ({one}) => ({
	event: one(events, {
		fields: [eventLocationShare.eventId],
		references: [events.id]
	}),
	profile: one(profile, {
		fields: [eventLocationShare.userId],
		references: [profile.id]
	}),
}));

export const billiardGamePlayerRelations = relations(billiardGamePlayer, ({one}) => ({
	billiardGameSession: one(billiardGameSession, {
		fields: [billiardGamePlayer.gameSessionId],
		references: [billiardGameSession.id]
	}),
	profile: one(profile, {
		fields: [billiardGamePlayer.playerId],
		references: [profile.id]
	}),
}));

export const billiardTransactionRelations = relations(billiardTransaction, ({one}) => ({
	billiardGameSession: one(billiardGameSession, {
		fields: [billiardTransaction.gameSessionId],
		references: [billiardGameSession.id]
	}),
	billiardGameRound: one(billiardGameRound, {
		fields: [billiardTransaction.roundId],
		references: [billiardGameRound.id]
	}),
	profile_fromPlayerId: one(profile, {
		fields: [billiardTransaction.fromPlayerId],
		references: [profile.id],
		relationName: "billiardTransaction_fromPlayerId_profile_id"
	}),
	profile_toPlayerId: one(profile, {
		fields: [billiardTransaction.toPlayerId],
		references: [profile.id],
		relationName: "billiardTransaction_toPlayerId_profile_id"
	}),
}));

export const chessGamePlayerRelations = relations(chessGamePlayer, ({one}) => ({
	chessGameSession: one(chessGameSession, {
		fields: [chessGamePlayer.gameSessionId],
		references: [chessGameSession.id]
	}),
	profile: one(profile, {
		fields: [chessGamePlayer.playerId],
		references: [profile.id]
	}),
}));

export const chessRoundPlayerRelations = relations(chessRoundPlayer, ({one}) => ({
	chessGameRound: one(chessGameRound, {
		fields: [chessRoundPlayer.roundId],
		references: [chessGameRound.id]
	}),
	chessGameSession: one(chessGameSession, {
		fields: [chessRoundPlayer.gameSessionId],
		references: [chessGameSession.id]
	}),
	profile: one(profile, {
		fields: [chessRoundPlayer.playerId],
		references: [profile.id]
	}),
}));

export const chessTransactionRelations = relations(chessTransaction, ({one}) => ({
	profile_fromPlayerId: one(profile, {
		fields: [chessTransaction.fromPlayerId],
		references: [profile.id],
		relationName: "chessTransaction_fromPlayerId_profile_id"
	}),
	chessGameSession: one(chessGameSession, {
		fields: [chessTransaction.gameSessionId],
		references: [chessGameSession.id]
	}),
	chessGameRound: one(chessGameRound, {
		fields: [chessTransaction.roundId],
		references: [chessGameRound.id]
	}),
	profile_toPlayerId: one(profile, {
		fields: [chessTransaction.toPlayerId],
		references: [profile.id],
		relationName: "chessTransaction_toPlayerId_profile_id"
	}),
}));